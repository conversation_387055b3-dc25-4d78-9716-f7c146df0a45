from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.modules.customers.models import Customer
from app import db
from app.modules.customers.forms import CustomerForm
from app.utils.decorators import permission_required
from . import bp

@bp.route('/')
@login_required
@permission_required('can_manage_customers')
def index():
    page = request.args.get('page', 1, type=int)
    owner_id = current_user.get_owner_id
    customers = Customer.query.filter_by(owner_id=owner_id).order_by(Customer.last_name).paginate(
        page=page, per_page=20, error_out=False)
    return render_template('customers/index.html', customers=customers)

@bp.route('/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_customers')
def new():
    form = CustomerForm()
    if form.validate_on_submit():
        customer = Customer(
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            email=form.email.data,
            phone=form.phone.data,
            address=form.address.data,
            owner_id=current_user.get_owner_id  # Ajout de owner_id
        )
        db.session.add(customer)
        db.session.commit()
        flash('Client ajouté avec succès!', 'success')
        return redirect(url_for('customers.index'))
    return render_template('customers/form.html', form=form, title="Nouveau Client")

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_customers')
def edit(id):
    customer = Customer.query.get_or_404(id)
    form = CustomerForm(obj=customer)
    if form.validate_on_submit():
        customer.first_name = form.first_name.data
        customer.last_name = form.last_name.data
        customer.email = form.email.data
        customer.phone = form.phone.data
        customer.address = form.address.data
        db.session.commit()
        flash('Client mis à jour avec succès!', 'success')
        return redirect(url_for('customers.index'))
    return render_template('customers/form.html', form=form, customer=customer, title="Modifier Client")

@bp.route('/<int:id>')
@login_required
@permission_required('can_manage_customers')
def show(id):
    customer = Customer.query.get_or_404(id)
    return render_template('customers/show.html', customer=customer)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_customers')
def delete(id):
    customer = Customer.query.get_or_404(id)
    db.session.delete(customer)
    db.session.commit()
    flash('Client supprimé avec succès!', 'success')
    return redirect(url_for('customers.index')) 