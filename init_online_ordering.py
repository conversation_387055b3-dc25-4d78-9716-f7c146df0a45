#!/usr/bin/env python3
"""
Script d'initialisation pour créer les tables et données nécessaires
au système de commande en ligne.
"""

import os
import sys
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.modules.auth.models import User, UserRole
from app.modules.online_ordering_sites.models import OnlineOrderingSite
from app.modules.settings.models_settings import Settings
from config import Config

def init_online_ordering():
    """Initialise le système de commande en ligne"""
    app = create_app(Config)
    
    with app.app_context():
        print("Création des tables pour le système de commande en ligne...")
        
        # Créer toutes les tables
        db.create_all()
        
        print("Tables créées avec succès!")
        
        # Créer le site all_businesses pour le system admin
        create_all_businesses_site()
        
        # Créer des sites de démonstration pour les owners existants
        create_demo_sites()
        
        print("Initialisation terminée!")

def create_all_businesses_site():
    """Crée le site spécial all_businesses pour le system admin"""
    print("Création du site all_businesses...")
    
    # Trouver le system admin
    system_admin = User.query.filter_by(role=UserRole.SYSTEM_ADMIN).first()
    
    if not system_admin:
        print("Aucun system admin trouvé. Création d'un system admin par défaut...")
        system_admin = User(
            username='admin',
            email='<EMAIL>',
            role=UserRole.SYSTEM_ADMIN,
            is_active=True
        )
        system_admin.set_password('admin123')
        db.session.add(system_admin)
        db.session.commit()
        print("System admin créé: admin / admin123")
    
    # Vérifier si le site all_businesses existe déjà
    existing_site = OnlineOrderingSite.query.filter_by(subdomain='all_businesses').first()
    
    if not existing_site:
        all_businesses_site = OnlineOrderingSite(
            owner_id=system_admin.id,
            subdomain='all_businesses',
            site_name='Tous les Restaurants',
            site_description='Découvrez tous nos restaurants partenaires et commandez en ligne',
            is_active=True,
            allow_online_ordering=True,
            allow_delivery=True,
            allow_pickup=True,
            allow_dine_in=True
        )
        db.session.add(all_businesses_site)
        db.session.commit()
        print("Site all_businesses créé avec succès!")
    else:
        print("Site all_businesses existe déjà.")

def create_demo_sites():
    """Crée des sites de démonstration pour les owners existants"""
    print("Création de sites de démonstration...")
    
    owners = User.query.filter_by(role=UserRole.OWNER).all()
    
    for owner in owners:
        # Vérifier si l'owner a déjà un site
        existing_site = OnlineOrderingSite.query.filter_by(owner_id=owner.id).first()
        
        if not existing_site:
            # Créer un sous-domaine basé sur le nom d'utilisateur
            subdomain = owner.username.lower().replace(' ', '_')
            
            # Vérifier que le sous-domaine n'existe pas déjà
            counter = 1
            original_subdomain = subdomain
            while OnlineOrderingSite.query.filter_by(subdomain=subdomain).first():
                subdomain = f"{original_subdomain}_{counter}"
                counter += 1
            
            # Récupérer les settings de l'owner
            settings = Settings.query.filter_by(owner_id=owner.id).first()
            
            site = OnlineOrderingSite(
                owner_id=owner.id,
                subdomain=subdomain,
                site_name=settings.business_name if settings else f"Restaurant {owner.username}",
                site_description=f"Commandez en ligne chez {settings.business_name if settings else owner.username}",
                is_active=False,  # Désactivé par défaut, l'owner doit l'activer
                allow_online_ordering=True,
                allow_delivery=True,
                allow_pickup=True,
                allow_dine_in=True,
                delivery_fee=2.50,
                minimum_order=15.00,
                delivery_radius=10.0
            )
            
            db.session.add(site)
            print(f"Site créé pour {owner.username}: {subdomain}.lvh.me:5000")
    
    db.session.commit()
    print("Sites de démonstration créés!")

def update_existing_settings():
    """Met à jour les settings existants avec les nouveaux champs"""
    print("Mise à jour des settings existants...")
    
    settings_list = Settings.query.all()
    
    for settings in settings_list:
        # Ajouter les valeurs par défaut pour les nouveaux champs
        if not hasattr(settings, 'online_ordering_enabled') or settings.online_ordering_enabled is None:
            settings.online_ordering_enabled = False
        
        # Récupérer le site correspondant
        site = OnlineOrderingSite.query.filter_by(owner_id=settings.owner_id).first()
        if site:
            settings.online_ordering_subdomain = site.subdomain
            settings.online_ordering_site_name = site.site_name
            settings.online_ordering_description = site.site_description
            settings.delivery_enabled = site.allow_delivery
            settings.pickup_enabled = site.allow_pickup
            settings.dine_in_enabled = site.allow_dine_in
            settings.delivery_fee = site.delivery_fee
            settings.minimum_order_amount = site.minimum_order
            settings.delivery_radius = site.delivery_radius
    
    db.session.commit()
    print("Settings mis à jour!")

if __name__ == '__main__':
    init_online_ordering()
