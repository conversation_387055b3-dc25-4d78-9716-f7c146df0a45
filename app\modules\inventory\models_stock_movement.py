from datetime import datetime
from app import db

class StockMovementType:
    IN = 'in'
    OUT = 'out'

class StockMovementReason:
    PURCHASE = 'purchase'
    SALE = 'sale'
    LOSS = 'loss'
    ADJUSTMENT = 'adjustment'
    RECIPE = 'recipe'
    OTHER = 'other'

class StockMovement(db.Model):
    __tablename__ = 'stock_movements'
    
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.Foreign<PERSON>ey('users.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # L'article peut être soit un produit soit un ingrédient
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'))
    
    type = db.Column(db.String(10), nullable=False)  # 'in' ou 'out'
    reason = db.Column(db.String(20), nullable=False)  # 'purchase', 'sale', 'loss', 'adjustment', 'recipe'
    quantity = db.Column(db.Float, nullable=False)
    previous_quantity = db.Column(db.Float, nullable=False)  # Stock avant le mouvement
    new_quantity = db.Column(db.Float, nullable=False)  # Stock après le mouvement
    notes = db.Column(db.Text)
    reference = db.Column(db.String(50))  # Référence de la vente, de l'achat, etc.
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    product = db.relationship('Product', backref='stock_movements')
    ingredient = db.relationship('Ingredient', backref='stock_movements')
    user = db.relationship('User', foreign_keys=[user_id], backref='stock_movements_made')
    owner = db.relationship('User', foreign_keys=[owner_id], backref='stock_movements_owned')
    
    def __repr__(self):
        item_name = self.product.name if self.product else self.ingredient.name
        return f'<StockMovement {self.type} {self.quantity} {item_name}>' 