#!/usr/bin/env python3
"""
Script pour créer des produits et catégories de démonstration
"""

import os
import sys

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.modules.auth.models import User, UserRole
from app.modules.inventory.models_product import Product, ProductCategory
from config import Config

def create_demo_products():
    """Crée des produits et catégories de démonstration"""
    app = create_app(Config)
    
    with app.app_context():
        # Trouver les owners
        owners = User.query.filter_by(role=UserRole.OWNER).all()
        
        for owner in owners:
            print(f"Création de produits pour {owner.username}...")
            
            # Vérifier si l'owner a déjà des catégories
            existing_categories = ProductCategory.query.filter_by(owner_id=owner.id).count()
            if existing_categories > 0:
                print(f"  {owner.username} a déjà {existing_categories} catégories, passage au suivant")
                continue
            
            # Créer des catégories
            categories_data = [
                {
                    'name': 'Entrées',
                    'description': 'Nos délicieuses entrées pour commencer votre repas',
                    'products': [
                        {'name': 'Salade César', 'description': 'Salade fraîche avec croûtons et parmesan', 'price': 8.50},
                        {'name': 'Soupe du jour', 'description': 'Soupe maison préparée avec des légumes frais', 'price': 6.00},
                        {'name': 'Bruschetta', 'description': 'Pain grillé avec tomates, basilic et mozzarella', 'price': 7.50},
                    ]
                },
                {
                    'name': 'Plats principaux',
                    'description': 'Nos spécialités pour satisfaire votre appétit',
                    'products': [
                        {'name': 'Pizza Margherita', 'description': 'Pizza classique avec tomates, mozzarella et basilic', 'price': 12.00},
                        {'name': 'Burger du Chef', 'description': 'Burger maison avec frites et salade', 'price': 15.50},
                        {'name': 'Pâtes Carbonara', 'description': 'Pâtes fraîches avec lardons et crème', 'price': 13.00},
                        {'name': 'Saumon grillé', 'description': 'Filet de saumon avec légumes de saison', 'price': 18.00},
                        {'name': 'Couscous Royal', 'description': 'Couscous traditionnel avec viandes et légumes', 'price': 16.50},
                    ]
                },
                {
                    'name': 'Desserts',
                    'description': 'Terminez votre repas en beauté',
                    'products': [
                        {'name': 'Tiramisu', 'description': 'Dessert italien traditionnel au café', 'price': 6.50},
                        {'name': 'Tarte aux pommes', 'description': 'Tarte maison avec glace vanille', 'price': 5.50},
                        {'name': 'Mousse au chocolat', 'description': 'Mousse onctueuse au chocolat noir', 'price': 5.00},
                    ]
                },
                {
                    'name': 'Boissons',
                    'description': 'Boissons chaudes et froides',
                    'products': [
                        {'name': 'Café Espresso', 'description': 'Café italien traditionnel', 'price': 2.50},
                        {'name': 'Thé à la menthe', 'description': 'Thé vert à la menthe fraîche', 'price': 3.00},
                        {'name': 'Jus d\'orange frais', 'description': 'Jus pressé minute', 'price': 4.00},
                        {'name': 'Coca-Cola', 'description': 'Boisson gazeuse 33cl', 'price': 2.50},
                        {'name': 'Eau minérale', 'description': 'Bouteille d\'eau 50cl', 'price': 2.00},
                    ]
                }
            ]
            
            for cat_data in categories_data:
                # Créer la catégorie
                category = ProductCategory(
                    name=cat_data['name'],
                    description=cat_data['description'],
                    owner_id=owner.id
                )
                db.session.add(category)
                db.session.flush()  # Pour obtenir l'ID
                
                print(f"  Catégorie créée: {category.name}")
                
                # Créer les produits
                for prod_data in cat_data['products']:
                    product = Product(
                        name=prod_data['name'],
                        description=prod_data['description'],
                        price=prod_data['price'],
                        category_id=category.id,
                        owner_id=owner.id,
                        stock_quantity=100,  # Stock initial
                        unit='unité'
                    )
                    db.session.add(product)
                    print(f"    Produit créé: {product.name} - {product.price}€")
            
            db.session.commit()
            print(f"  Produits créés pour {owner.username} ✓")
        
        print("\nCréation des produits de démonstration terminée!")

if __name__ == '__main__':
    create_demo_products()
