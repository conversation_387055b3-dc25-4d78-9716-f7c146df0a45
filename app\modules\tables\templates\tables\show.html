{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h1>Table {{ table.number }}</h1>
                <div>
                    <a href="{{ url_for('tables.reserve', id=table.id) }}" class="btn btn-primary">
                        <i class="fas fa-calendar-plus"></i> Réserver
                    </a>
                    <a href="{{ url_for('tables.edit', id=table.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Modifier
                    </a>
                    <form action="{{ url_for('tables.delete', id=table.id) }}" method="POST" class="d-inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-outline-danger" 
                                onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette table ?')">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations de la table -->
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informations</h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">Numéro</dt>
                        <dd class="col-sm-8">{{ table.number }}</dd>

                        <dt class="col-sm-4">Capacité</dt>
                        <dd class="col-sm-8">{{ table.capacity }} personnes</dd>

                        <dt class="col-sm-4">Emplacement</dt>
                        <dd class="col-sm-8">{{ table.location or 'Non spécifié' }}</dd>

                        <dt class="col-sm-4">Statut</dt>
                        <dd class="col-sm-8">
                            {% if table.status == 'AVAILABLE' %}
                            <span class="badge bg-success">Disponible</span>
                            {% elif table.status == 'OCCUPIED' %}
                            <span class="badge bg-danger">Occupée</span>
                            {% elif table.status == 'RESERVED' %}
                            <span class="badge bg-warning">Réservée</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ table.status }}</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Réservations du jour -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Réservations du jour</h5>
                </div>
                <div class="card-body">
                    {% if table.reservations %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Heure</th>
                                    <th>Client</th>
                                    <th>Personnes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reservation in table.reservations %}
                                {% if reservation.reservation_date.date() == today %}
                                <tr>
                                    <td>{{ reservation.reservation_date.strftime('%H:%M') }}</td>
                                    <td>
                                        {{ reservation.customer_name }}
                                        {% if reservation.customer_phone %}
                                        <br>
                                        <small class="text-muted">{{ reservation.customer_phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ reservation.number_of_guests }}</td>
                                    <td>
                                        <form action="{{ url_for('tables.cancel_reservation', id=reservation.id) }}" 
                                              method="POST" 
                                              class="d-inline">
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-danger"
                                                    onclick="return confirm('Annuler cette réservation ?')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">Aucune réservation pour aujourd'hui</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Commande en cours -->
    {% if table.current_order %}
    <div class="row">
        <div class="col">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Commande en cours</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Quantité</th>
                                    <th>Prix unitaire</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in table.current_order.items %}
                                <tr>
                                    <td>{{ item.product.name }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ "%.2f"|format(item.unit_price) }} €</td>
                                    <td>{{ "%.2f"|format(item.total) }} €</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-end">Total</th>
                                    <th>{{ "%.2f"|format(table.current_order.total) }} €</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %} 