from flask import render_template, request, redirect, url_for, flash, jsonify, session, current_app
from flask_login import login_user, logout_user, login_required, current_user
from urllib.parse import urlparse
from datetime import datetime
from app.extensions import db
from app.modules.online_ordering_sites.models import (
    OnlineOrderingSite, CustomerUser, OnlineOrder, OnlineOrderItem,
    DeliveryAddress, OrderType, OnlineOrderStatus, PaymentStatus, PaymentMethod
)
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.auth.models import User, UserRole
from app.modules.settings.models_settings import Settings
from . import bp
import re

def get_subdomain():
    """Extrait le sous-domaine de la requête"""
    host = request.host.lower()
    
    # Pour le développement local avec lvh.me
    if 'lvh.me' in host:
        subdomain = host.split('.')[0]
        return subdomain
    
    # Pour la production avec un domaine personnalisé
    # Exemple: resto1.mondomaine.com
    parts = host.split('.')
    if len(parts) > 2:
        return parts[0]
    
    return None

def get_site_by_subdomain(subdomain):
    """Récupère le site de commande en ligne par sous-domaine"""
    if not subdomain:
        return None
    
    # Cas spécial pour all_businesses (system admin)
    if subdomain == 'all_businesses':
        return 'all_businesses'
    
    # Recherche du site par sous-domaine
    site = OnlineOrderingSite.query.filter_by(
        subdomain=subdomain, 
        is_active=True
    ).first()
    
    return site

# Le middleware de routage est maintenant géré au niveau global dans app/__init__.py

@bp.route('/')
def index():
    """Page d'accueil du site de commande"""
    subdomain = session.get('current_subdomain')
    site_type = session.get('site_type')
    
    if site_type == 'all_businesses':
        return all_businesses_index()
    
    site_id = session.get('current_site_id')
    if not site_id:
        return redirect(url_for('main.index'))
    
    site = OnlineOrderingSite.query.get_or_404(site_id)
    
    # Récupérer les produits du restaurant
    owner_id = site.owner_id
    categories = ProductCategory.query.filter_by(owner_id=owner_id).all()
    featured_products = Product.query.filter_by(
        owner_id=owner_id, 
        is_active=True
    ).limit(8).all()
    
    return render_template('online_ordering_sites/index.html',
                         site=site,
                         categories=categories,
                         featured_products=featured_products)

def all_businesses_index():
    """Page d'accueil pour le site all_businesses (system admin)"""
    # Vérifier que seul le system admin peut accéder
    if current_user.is_authenticated and not current_user.is_system_admin:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('main.index'))
    
    # Récupérer tous les sites actifs groupés par type de business
    sites = db.session.query(OnlineOrderingSite, Settings).join(
        Settings, OnlineOrderingSite.owner_id == Settings.owner_id
    ).filter(OnlineOrderingSite.is_active == True).all()
    
    # Grouper par type de business
    businesses_by_type = {}
    for site, settings in sites:
        business_type = settings.business_type or 'Restaurant'
        if business_type not in businesses_by_type:
            businesses_by_type[business_type] = []
        businesses_by_type[business_type].append({
            'site': site,
            'settings': settings
        })
    
    return render_template('online_ordering_sites/all_businesses.html',
                         businesses_by_type=businesses_by_type)

@bp.route('/menu')
def menu():
    """Page du menu complet"""
    site_id = session.get('current_site_id')
    if not site_id:
        return redirect(url_for('online_ordering_sites.index'))

    site = OnlineOrderingSite.query.get_or_404(site_id)
    owner_id = site.owner_id

    # Récupérer toutes les catégories et produits
    categories = ProductCategory.query.filter_by(owner_id=owner_id).all()

    # Organiser les produits par catégorie
    menu_data = {}
    for category in categories:
        products = Product.query.filter_by(
            category_id=category.id,
            is_active=True
        ).all()
        if products:  # Seulement les catégories avec des produits
            menu_data[category] = products

    # Récupérer les favoris du client s'il est connecté
    customer_favorites = []
    customer_id = session.get('customer_id')
    if customer_id:
        customer = CustomerUser.query.get(customer_id)
        if customer:
            customer_favorites = [p.id for p in customer.favorite_products]

    return render_template('online_ordering_sites/menu.html',
                         site=site,
                         menu_data=menu_data,
                         customer_favorites=customer_favorites)

@bp.route('/category/<int:category_id>')
def category_products(category_id):
    """Produits d'une catégorie spécifique"""
    site_id = session.get('current_site_id')
    if not site_id:
        return redirect(url_for('online_ordering_sites.index'))
    
    site = OnlineOrderingSite.query.get_or_404(site_id)
    category = ProductCategory.query.filter_by(
        id=category_id,
        owner_id=site.owner_id
    ).first_or_404()
    
    products = Product.query.filter_by(
        category_id=category_id,
        is_active=True
    ).all()
    
    return render_template('online_ordering_sites/category_products.html',
                         site=site,
                         category=category,
                         products=products)

@bp.route('/product/<int:product_id>')
def product_detail(product_id):
    """Détail d'un produit"""
    site_id = session.get('current_site_id')
    if not site_id:
        return redirect(url_for('online_ordering_sites.index'))
    
    site = OnlineOrderingSite.query.get_or_404(site_id)
    product = Product.query.filter_by(
        id=product_id,
        owner_id=site.owner_id,
        is_active=True
    ).first_or_404()
    
    return render_template('online_ordering_sites/product_detail.html',
                         site=site,
                         product=product)

# Routes pour l'authentification des clients
@bp.route('/customer/register', methods=['GET', 'POST'])
def customer_register():
    """Inscription des clients"""
    site_id = session.get('current_site_id')
    if not site_id:
        return redirect(url_for('online_ordering_sites.index'))

    site = OnlineOrderingSite.query.get_or_404(site_id)

    from app.modules.online_ordering_sites.forms import CustomerRegistrationForm
    form = CustomerRegistrationForm()

    if form.validate_on_submit():
        # Créer le nouveau client
        customer = CustomerUser(
            email=form.email.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            phone=form.phone.data,
            address=form.address.data,
            city=form.city.data,
            country=form.country.data,
            postal_code=form.postal_code.data,
            email_notifications=form.email_notifications.data,
            sms_notifications=form.sms_notifications.data
        )
        customer.set_password(form.password.data)

        db.session.add(customer)
        db.session.commit()

        # Connecter automatiquement le client
        session['customer_id'] = customer.id
        session['customer_email'] = customer.email

        flash('Votre compte a été créé avec succès ! Bienvenue !', 'success')
        return redirect(url_for('online_ordering_sites.index'))

    return render_template('online_ordering_sites/customer_register.html', site=site, form=form)

@bp.route('/customer/login', methods=['GET', 'POST'])
def customer_login():
    """Connexion des clients"""
    site_id = session.get('current_site_id')
    if not site_id:
        return redirect(url_for('online_ordering_sites.index'))

    site = OnlineOrderingSite.query.get_or_404(site_id)

    from app.modules.online_ordering_sites.forms import CustomerLoginForm
    form = CustomerLoginForm()

    if form.validate_on_submit():
        customer = CustomerUser.query.filter_by(email=form.email.data).first()

        if customer and customer.check_password(form.password.data):
            if not customer.is_active:
                flash('Votre compte a été désactivé. Contactez le support.', 'error')
                return redirect(url_for('online_ordering_sites.customer_login'))

            # Connecter le client
            session['customer_id'] = customer.id
            session['customer_email'] = customer.email

            # Mettre à jour la dernière connexion
            from datetime import datetime
            customer.last_login = datetime.utcnow()
            db.session.commit()

            flash(f'Bienvenue {customer.first_name} !', 'success')

            # Rediriger vers la page demandée ou l'accueil
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('online_ordering_sites.index'))
        else:
            flash('Email ou mot de passe incorrect.', 'error')

    return render_template('online_ordering_sites/customer_login.html', site=site, form=form)

@bp.route('/customer/logout')
def customer_logout():
    """Déconnexion des clients"""
    # Nettoyer la session client
    session.pop('customer_id', None)
    flash('Vous avez été déconnecté avec succès', 'info')
    return redirect(url_for('online_ordering_sites.index'))

@bp.route('/customer/profile')
def customer_profile():
    """Profil et historique des commandes du client"""
    site_id = session.get('current_site_id')
    customer_id = session.get('customer_id')

    if not site_id or not customer_id:
        flash('Veuillez vous connecter pour accéder à votre profil', 'info')
        return redirect(url_for('online_ordering_sites.customer_login'))

    site = OnlineOrderingSite.query.get_or_404(site_id)
    customer = CustomerUser.query.get_or_404(customer_id)

    # Récupérer l'historique des commandes du client pour ce site
    orders = OnlineOrder.query.filter_by(
        site_id=site_id,
        customer_id=customer_id
    ).order_by(OnlineOrder.ordered_at.desc()).all()

    return render_template('online_ordering_sites/customer_profile.html',
                         site=site,
                         customer=customer,
                         orders=orders)

@bp.route('/customer/orders')
def customer_orders():
    """Historique des commandes du client"""
    site_id = session.get('current_site_id')
    customer_id = session.get('customer_id')

    if not site_id or not customer_id:
        flash('Veuillez vous connecter pour voir vos commandes', 'info')
        return redirect(url_for('online_ordering_sites.customer_login'))

    site = OnlineOrderingSite.query.get_or_404(site_id)
    customer = CustomerUser.query.get_or_404(customer_id)

    # Pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10

    # Récupérer les commandes avec pagination
    orders = OnlineOrder.query.filter_by(
        site_id=site_id,
        customer_id=customer_id
    ).order_by(OnlineOrder.ordered_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('online_ordering_sites/customer_orders.html',
                         site=site,
                         customer=customer,
                         orders=orders)

# API Routes pour AJAX
@bp.route('/api/add_to_cart', methods=['POST'])
def add_to_cart():
    """Ajouter un produit au panier (AJAX)"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 1))

        # Vérifier que le produit existe et est disponible
        from app.modules.inventory.models_product import Product
        product = Product.query.get_or_404(product_id)

        if not product.is_available:
            return jsonify({'status': 'error', 'message': 'Produit non disponible'})

        # Récupérer le panier de la session
        cart_key = f"cart_{session.get('current_site_id')}"
        cart = session.get(cart_key, [])

        # Vérifier si le produit est déjà dans le panier
        existing_item = next((item for item in cart if item['product_id'] == product_id), None)

        if existing_item:
            existing_item['quantity'] += quantity
        else:
            cart.append({
                'product_id': product_id,
                'product_name': product.name,
                'price': float(product.price),
                'quantity': quantity,
                'image_path': product.image_path
            })

        session[cart_key] = cart

        # Calculer le total
        total_items = sum(item['quantity'] for item in cart)
        total_amount = sum(item['price'] * item['quantity'] for item in cart)

        return jsonify({
            'status': 'success',
            'message': 'Produit ajouté au panier',
            'cart_count': total_items,
            'cart_total': total_amount
        })

    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@bp.route('/api/update_cart', methods=['POST'])
def update_cart():
    """Mettre à jour le panier (AJAX)"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 0))

        cart_key = f"cart_{session.get('current_site_id')}"
        cart = session.get(cart_key, [])

        if quantity <= 0:
            # Supprimer l'article du panier
            cart = [item for item in cart if item['product_id'] != product_id]
        else:
            # Mettre à jour la quantité
            for item in cart:
                if item['product_id'] == product_id:
                    item['quantity'] = quantity
                    break

        session[cart_key] = cart

        # Calculer le total
        total_items = sum(item['quantity'] for item in cart)
        total_amount = sum(item['price'] * item['quantity'] for item in cart)

        return jsonify({
            'status': 'success',
            'cart_count': total_items,
            'cart_total': total_amount
        })

    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@bp.route('/api/get_cart')
def get_cart():
    """Récupérer le contenu du panier (AJAX)"""
    cart_key = f"cart_{session.get('current_site_id')}"
    cart = session.get(cart_key, [])

    total_items = sum(item['quantity'] for item in cart)
    total_amount = sum(item['price'] * item['quantity'] for item in cart)

    return jsonify({
        'items': cart,
        'total_items': total_items,
        'total_amount': total_amount
    })

@bp.route('/api/toggle_favorite', methods=['POST'])
def toggle_favorite():
    """Ajouter/retirer un produit des favoris (AJAX)"""
    print("=" * 50)
    print("DEBUG toggle_favorite: DÉBUT DE LA REQUÊTE")
    print(f"DEBUG: URL = {request.url}")
    print(f"DEBUG: Method = {request.method}")
    print(f"DEBUG: Headers = {dict(request.headers)}")
    print(f"DEBUG: Content-Type = {request.content_type}")
    print(f"DEBUG: is_json = {request.is_json}")
    print(f"DEBUG: Raw data = {request.get_data()}")
    print(f"DEBUG: Session complète = {dict(session)}")

    # Essayer de récupérer les données JSON de manière flexible
    try:
        if request.is_json:
            data = request.get_json()
            print(f"DEBUG: Données JSON via get_json() = {data}")
        else:
            # Essayer de parser manuellement si le content-type n'est pas correct
            import json
            raw_data = request.get_data()
            if raw_data:
                data = json.loads(raw_data.decode('utf-8'))
                print(f"DEBUG: Données JSON via parsing manuel = {data}")
            else:
                data = None
                print("DEBUG: Aucune donnée reçue")
    except Exception as e:
        print(f"DEBUG: ERREUR parsing JSON = {e}")
        return jsonify({'status': 'error', 'message': f'Erreur de parsing JSON: {str(e)}'}), 400

    # Vérifier la session du site
    site_id = session.get('current_site_id')
    print(f"DEBUG: site_id = {site_id}")
    if not site_id:
        print("DEBUG: ERREUR - Pas de site_id dans la session")
        return jsonify({'status': 'error', 'message': 'Session site invalide'}), 400

    # Vérifier la connexion du client
    customer_id = session.get('customer_id')
    print(f"DEBUG: customer_id = {customer_id}")
    if not customer_id:
        print("DEBUG: ERREUR - Pas de customer_id dans la session")
        return jsonify({
            'status': 'error',
            'message': 'Veuillez vous connecter pour gérer vos favoris',
            'redirect': url_for('online_ordering_sites.customer_login')
        }), 401

    try:
        print(f"DEBUG: Vérification des données...")
        if not data:
            print("DEBUG: ERREUR - Aucune donnée reçue")
            return jsonify({'status': 'error', 'message': 'Aucune donnée reçue'}), 400

        product_id = data.get('product_id')
        print(f"DEBUG: product_id extrait = {product_id}")
        if not product_id:
            print("DEBUG: ERREUR - ID produit manquant")
            return jsonify({'status': 'error', 'message': 'ID produit manquant'}), 400

        from app.modules.inventory.models_product import Product

        # Vérifier que le produit existe
        product = Product.query.get(product_id)
        print(f"DEBUG: Produit trouvé = {product}")
        if not product:
            print("DEBUG: ERREUR - Produit non trouvé")
            return jsonify({'status': 'error', 'message': 'Produit non trouvé'}), 404

        # Vérifier que le client existe
        customer = CustomerUser.query.get(customer_id)
        print(f"DEBUG: Client trouvé = {customer}")
        if not customer:
            print("DEBUG: ERREUR - Client non trouvé")
            return jsonify({'status': 'error', 'message': 'Client non trouvé'}), 404

        # Vérifier si le produit est déjà en favori
        is_favorite = product in customer.favorite_products
        print(f"DEBUG: is_favorite = {is_favorite}")

        if is_favorite:
            # Retirer des favoris
            customer.favorite_products.remove(product)
            message = 'Produit retiré des favoris'
            action = 'removed'
            print("DEBUG: Produit retiré des favoris")
        else:
            # Ajouter aux favoris
            customer.favorite_products.append(product)
            message = 'Produit ajouté aux favoris'
            action = 'added'
            print("DEBUG: Produit ajouté aux favoris")

        db.session.commit()
        print("DEBUG: Changements sauvegardés en base")

        result = {
            'status': 'success',
            'message': message,
            'action': action,
            'is_favorite': not is_favorite
        }
        print(f"DEBUG: Résultat à retourner = {result}")
        print("DEBUG toggle_favorite: FIN DE LA REQUÊTE - SUCCÈS")
        print("=" * 50)
        return jsonify(result)

    except Exception as e:
        print(f"DEBUG: EXCEPTION = {e}")
        print(f"DEBUG: Type exception = {type(e)}")
        import traceback
        print(f"DEBUG: Traceback = {traceback.format_exc()}")
        db.session.rollback()
        print("DEBUG toggle_favorite: FIN DE LA REQUÊTE - ERREUR")
        print("=" * 50)
        return jsonify({'status': 'error', 'message': f'Erreur serveur: {str(e)}'}), 500

@bp.route('/customer/favorites')
def customer_favorites():
    """Page des favoris du client"""
    site_id = session.get('current_site_id')
    customer_id = session.get('customer_id')

    if not site_id or not customer_id:
        flash('Veuillez vous connecter pour voir vos favoris', 'info')
        return redirect(url_for('online_ordering_sites.customer_login'))

    site = OnlineOrderingSite.query.get_or_404(site_id)
    customer = CustomerUser.query.get_or_404(customer_id)

    # Récupérer les produits favoris du client
    favorite_products = customer.favorite_products

    return render_template('online_ordering_sites/customer_favorites.html',
                         site=site,
                         customer=customer,
                         favorite_products=favorite_products)

@bp.route('/checkout')
def checkout():
    """Page de commande"""
    site_id = session.get('current_site_id')
    if not site_id:
        return redirect(url_for('online_ordering_sites.index'))

    # Vérifier que le client est connecté
    customer_id = session.get('customer_id')
    if not customer_id:
        flash('Veuillez vous connecter pour passer commande', 'info')
        return redirect(url_for('online_ordering_sites.customer_login', next=request.url))

    site = OnlineOrderingSite.query.get_or_404(site_id)
    customer = CustomerUser.query.get_or_404(customer_id)

    # Le panier sera récupéré côté client via JavaScript
    # Nous passons juste les informations de base
    delivery_fee = site.delivery_fee if site.allow_delivery else 0

    return render_template('online_ordering_sites/checkout.html',
                         site=site,
                         customer=customer,
                         delivery_fee=delivery_fee)

@bp.route('/test_route', methods=['GET', 'POST'])
def test_route():
    """Route de test"""
    print("DEBUG: test_route called")
    return jsonify({'status': 'success', 'message': 'Route de test fonctionne'})



@bp.route('/place_order', methods=['GET', 'POST'])
def place_order():
    """Passer une commande"""
    print(f"DEBUG: place_order called - START - Method: {request.method}")

    if request.method == 'GET':
        return jsonify({'status': 'success', 'message': 'Route place_order accessible', 'method': 'GET'})

    try:
        site_id = session.get('current_site_id')
        customer_id = session.get('customer_id')
        print(f"DEBUG: site_id={site_id}, customer_id={customer_id}")

        if not site_id or not customer_id:
            print(f"DEBUG: Session invalide - site_id={site_id}, customer_id={customer_id}")
            return jsonify({'status': 'error', 'message': 'Session invalide'}), 400
    except Exception as e:
        print(f"DEBUG: Erreur dans la vérification de session: {e}")
        return jsonify({'status': 'error', 'message': f'Erreur session: {str(e)}'}), 500

    try:
        data = request.get_json()
        print(f"DEBUG: Données reçues: {data}")

        order_type = data.get('order_type')
        payment_method = data.get('payment_method', 'cash_on_delivery')
        customer_notes = data.get('customer_notes', '')
        delivery_address_id = data.get('delivery_address_id')
        cart_items = data.get('cart_items', [])

        print(f"DEBUG: cart_items: {cart_items}")
        print(f"DEBUG: order_type: {order_type}, payment_method: {payment_method}")

        if not cart_items:
            return jsonify({'status': 'error', 'message': 'Panier vide'})

        site = OnlineOrderingSite.query.get_or_404(site_id)
        customer = CustomerUser.query.get_or_404(customer_id)

        # Vérifier la disponibilité des stocks avant de créer la commande
        from app.modules.inventory.models_product import Product
        for item in cart_items:
            product_id = int(item['productId'])
            quantity = int(item['quantity'])

            product = Product.query.get(product_id)
            if not product:
                return jsonify({'status': 'error', 'message': f'Produit non trouvé (ID: {product_id})'})

            # Vérifier la disponibilité selon le type de produit
            if product.has_recipe:
                # Pour les produits avec recette, vérifier la disponibilité des ingrédients
                if product.recipe:
                    possible_quantity = product.recipe.get_possible_quantity()
                    if possible_quantity < quantity:
                        return jsonify({
                            'status': 'error',
                            'message': f'Stock insuffisant pour {product.name}. Quantité possible: {possible_quantity}'
                        })
            else:
                # Pour les produits simples, vérifier le stock direct
                if product.stock_quantity < quantity:
                    return jsonify({
                        'status': 'error',
                        'message': f'Stock insuffisant pour {product.name}. Stock disponible: {product.stock_quantity}'
                    })

        # Calculer les totaux
        subtotal = sum(float(item['price']) * int(item['quantity']) for item in cart_items)
        delivery_fee = site.delivery_fee if order_type == 'delivery' else 0
        total = subtotal + delivery_fee

        # Générer un numéro de commande unique
        import random
        import string
        order_number = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

        # Gérer l'adresse de livraison
        delivery_address_text = None
        if order_type == 'delivery':
            if delivery_address_id:
                # Récupérer l'adresse depuis la base de données
                address_obj = DeliveryAddress.query.get(delivery_address_id)
                if address_obj and address_obj.customer_id == customer_id:
                    delivery_address_text = f"{address_obj.street_address}, {address_obj.city} {address_obj.postal_code}"
            else:
                # Utiliser l'adresse par défaut du client si disponible
                default_address = DeliveryAddress.query.filter_by(
                    customer_id=customer_id,
                    is_default=True
                ).first()
                if default_address:
                    delivery_address_text = f"{default_address.street_address}, {default_address.city} {default_address.postal_code}"
                    delivery_address_id = default_address.id

        # Créer la commande
        order = OnlineOrder(
            order_number=order_number,
            site_id=site_id,
            customer_id=customer_id,
            order_type=OrderType(order_type),
            subtotal=subtotal,
            delivery_fee=delivery_fee,
            total_amount=total,
            customer_notes=customer_notes,
            status=OnlineOrderStatus.CONFIRMED,  # Directement confirmée pour apparaître en cuisine
            payment_status=PaymentStatus.PENDING,
            payment_method=PaymentMethod(payment_method),
            delivery_address_id=delivery_address_id,
            delivery_address=delivery_address_text,
            confirmed_at=datetime.utcnow()  # Marquer comme confirmée maintenant
        )

        db.session.add(order)
        db.session.flush()  # Pour obtenir l'ID de la commande

        # Ajouter les articles de la commande et mettre à jour les stocks
        for item in cart_items:
            product_id = int(item['productId'])
            quantity = int(item['quantity'])

            order_item = OnlineOrderItem(
                order_id=order.id,
                product_id=product_id,
                quantity=quantity,
                unit_price=float(item['price']),
                total_price=float(item['price']) * quantity
            )
            db.session.add(order_item)

            # Mettre à jour le stock du produit
            from app.modules.inventory.models_product import Product
            product = Product.query.get(product_id)
            if product:
                try:
                    success = product.update_stock(
                        quantity,
                        operation='subtract',
                        reason='SALE',
                        reference=f"Online Order #{order.order_number}",
                        notes=f"Commande en ligne - {order.order_type.value}"
                    )
                    if not success:
                        db.session.rollback()
                        return jsonify({
                            'status': 'error',
                            'message': f'Stock insuffisant pour {product.name}. Quantité disponible: {product.stock_quantity if not product.has_recipe else "calculée selon recette"}'
                        })
                except Exception as e:
                    db.session.rollback()
                    return jsonify({
                        'status': 'error',
                        'message': f'Erreur lors de la mise à jour du stock pour {product.name}: {str(e)}'
                    })

        db.session.commit()

        # Envoyer une notification au restaurant
        try:
            from app.modules.notifications.services import NotificationService
            from app.modules.notifications.socketio_events import notify_new_order

            notification_service = NotificationService()

            # Créer une notification pour le propriétaire du restaurant
            notification_service.create_notification(
                type='order_received',
                title=f'Nouvelle commande #{order.order_number}',
                message=f'Une nouvelle commande de {customer.first_name} {customer.last_name} d\'un montant de {total:.2f}€ a été reçue.',
                recipient_type='restaurant',
                recipient_id=site.owner_id,
                order_id=order.id,
                data={
                    'order_number': order.order_number,
                    'customer_name': f'{customer.first_name} {customer.last_name}',
                    'total_amount': float(total),
                    'order_type': order_type,
                    'site_name': site.site_name
                }
            )

            # Envoyer notification en temps réel via SocketIO
            notify_new_order(order)

        except Exception as e:
            # Ne pas faire échouer la commande si la notification échoue
            print(f"Erreur lors de l'envoi de la notification: {e}")

        # Le panier sera vidé côté client via JavaScript

        return jsonify({
            'status': 'success',
            'message': 'Commande passée avec succès',
            'order_number': order_number,
            'order_id': order.id
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)})

@bp.route('/admin/orders/<int:order_id>/update_status', methods=['POST'])
@login_required
def update_order_status(order_id):
    """Mettre à jour le statut d'une commande en ligne"""
    try:
        # Récupérer le site du restaurateur
        site = OnlineOrderingSite.query.filter_by(owner_id=current_user.id).first()
        if not site:
            return jsonify({'status': 'error', 'message': 'Site non trouvé'}), 404

        # Récupérer la commande
        order = OnlineOrder.query.filter_by(id=order_id, site_id=site.id).first()
        if not order:
            return jsonify({'status': 'error', 'message': 'Commande non trouvée'}), 404

        # Récupérer le nouveau statut depuis la requête
        data = request.get_json()
        new_status = data.get('status')
        old_status = order.status.value

        if new_status == 'ready':
            order.status = OnlineOrderStatus.READY
            order.ready_at = datetime.utcnow()
            message = 'Commande marquée comme prête'
        elif new_status == 'delivered':
            order.status = OnlineOrderStatus.DELIVERED
            order.delivered_at = datetime.utcnow()
            # Si c'est un paiement à la livraison, marquer comme payé
            if order.payment_method == PaymentMethod.CASH_ON_DELIVERY:
                order.payment_status = PaymentStatus.PAID
            message = 'Commande marquée comme livrée'
        elif new_status == 'preparing':
            order.status = OnlineOrderStatus.PREPARING
            message = 'Commande en préparation'
        else:
            return jsonify({'status': 'error', 'message': 'Statut invalide'}), 400

        db.session.commit()

        # Envoyer notification en temps réel du changement de statut
        try:
            from app.modules.notifications.socketio_events import notify_order_status_change
            notify_order_status_change(order, old_status, new_status)
        except Exception as e:
            print(f"Erreur lors de l'envoi de la notification de changement de statut: {e}")

        return jsonify({
            'status': 'success',
            'message': message,
            'order_status': order.status.value
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)}), 500

@bp.route('/admin/orders')
@login_required
def admin_orders():
    """Page d'administration des commandes en ligne pour les restaurateurs"""
    # Récupérer le site du restaurateur
    site = OnlineOrderingSite.query.filter_by(owner_id=current_user.id).first()
    if not site:
        flash('Vous n\'avez pas de site de commande en ligne configuré.', 'warning')
        return redirect(url_for('settings.online_ordering'))

    # Récupérer les commandes
    orders = OnlineOrder.query.filter_by(site_id=site.id).order_by(OnlineOrder.ordered_at.desc()).all()

    # Calculer le nombre de commandes d'aujourd'hui
    from datetime import datetime, date
    today = date.today()
    today_orders_count = sum(1 for order in orders if order.ordered_at.date() == today)

    return render_template('online_ordering_sites/admin_orders.html',
                         site=site,
                         orders=orders,
                         today_orders_count=today_orders_count)



@bp.route('/admin/orders/<int:order_id>/details')
@login_required
def order_details_api(order_id):
    """API pour récupérer les détails d'une commande en ligne"""
    try:
        order = OnlineOrder.query.get_or_404(order_id)

        # Vérifier que l'utilisateur est propriétaire du site
        if order.site.owner_id != current_user.id:
            return jsonify({'status': 'error', 'message': 'Non autorisé'}), 403

        # Construire le HTML des détails
        details_html = f"""
        <div class="order-details">
            <h6>Commande #{order.order_number}</h6>
            <p><strong>Client:</strong> {order.customer.first_name} {order.customer.last_name}</p>
            <p><strong>Type:</strong> {order.order_type.value.replace('_', ' ').title()}</p>
            <p><strong>Méthode de paiement:</strong> {order.payment_method.value.replace('_', ' ').title()}</p>

            <h6>Articles:</h6>
            <ul class="list-group list-group-flush">
        """

        for item in order.items:
            details_html += f"""
                <li class="list-group-item d-flex justify-content-between">
                    <span>{item.product.name} x{item.quantity}</span>
                    <span>{item.total_price:.2f}€</span>
                </li>
            """

        details_html += f"""
            </ul>
            <div class="mt-3">
                <p><strong>Sous-total:</strong> {order.subtotal:.2f}€</p>
                {f'<p><strong>Frais de livraison:</strong> {order.delivery_fee:.2f}€</p>' if order.delivery_fee > 0 else ''}
                <p><strong>Total:</strong> {order.total_amount:.2f}€</p>
            </div>
        </div>
        """

        return jsonify({
            'status': 'success',
            'html': details_html,
            'total': order.total_amount
        })

    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@bp.route('/admin/orders/<int:order_id>/print')
@login_required
def print_order(order_id):
    """Imprimer une commande en ligne"""
    try:
        order = OnlineOrder.query.get_or_404(order_id)

        # Vérifier que l'utilisateur est propriétaire du site
        if order.site.owner_id != current_user.id:
            flash('Non autorisé', 'error')
            return redirect(url_for('online_ordering_sites.admin_orders'))

        return render_template('online_ordering_sites/print_order.html', order=order)

    except Exception as e:
        flash(f'Erreur lors de l\'impression: {str(e)}', 'error')
        return redirect(url_for('online_ordering_sites.admin_orders'))

@bp.route('/track/<order_number>')
def track_order(order_number):
    """Page de suivi de commande pour les clients"""
    site_id = session.get('current_site_id')
    if not site_id:
        return redirect(url_for('online_ordering_sites.index'))

    site = OnlineOrderingSite.query.get_or_404(site_id)

    # Récupérer la commande
    order = OnlineOrder.query.filter_by(
        order_number=order_number,
        site_id=site_id
    ).first_or_404()

    return render_template('online_ordering_sites/track_order.html',
                         site=site,
                         order=order)

@bp.route('/api/track/<order_number>')
def track_order_api(order_number):
    """API pour récupérer le statut d'une commande"""
    # Récupérer le site depuis le sous-domaine
    subdomain = request.host.split('.')[0] if '.' in request.host else None
    if not subdomain or subdomain in ['127', 'localhost']:
        # Fallback vers la session pour les accès directs
        site_id = session.get('current_site_id')
        if not site_id:
            return jsonify({'status': 'error', 'message': 'Site non trouvé'})
        site = OnlineOrderingSite.query.get(site_id)
    else:
        site = OnlineOrderingSite.query.filter_by(subdomain=subdomain).first()
        if not site:
            return jsonify({'status': 'error', 'message': 'Site non trouvé'})

    try:
        order = OnlineOrder.query.filter_by(
            order_number=order_number,
            site_id=site.id
        ).first_or_404()

        # Calculer le temps estimé
        estimated_time = None
        if order.status in [OnlineOrderStatus.PENDING, OnlineOrderStatus.CONFIRMED, OnlineOrderStatus.PREPARING]:
            # Estimation basée sur le type de commande
            if order.order_type == OrderType.DELIVERY:
                estimated_time = "30-45 minutes"
            elif order.order_type == OrderType.PICKUP:
                estimated_time = "15-20 minutes"
            else:
                estimated_time = "20-30 minutes"

        return jsonify({
            'status': 'success',
            'order': {
                'number': order.order_number,
                'status': order.status.value,
                'status_display': get_status_display(order.status),
                'order_type': order.order_type.value,
                'total': order.total_amount,
                'ordered_at': order.ordered_at.strftime('%d/%m/%Y %H:%M'),
                'estimated_time': estimated_time,
                'confirmed_at': order.confirmed_at.strftime('%d/%m/%Y %H:%M') if order.confirmed_at else None,
                'prepared_at': order.prepared_at.strftime('%d/%m/%Y %H:%M') if order.prepared_at else None,
                'ready_at': order.ready_at.strftime('%d/%m/%Y %H:%M') if order.ready_at else None,
                'out_for_delivery_at': order.out_for_delivery_at.strftime('%d/%m/%Y %H:%M') if order.out_for_delivery_at else None,
                'delivered_at': order.delivered_at.strftime('%d/%m/%Y %H:%M') if order.delivered_at else None,
            }
        })

    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

def get_status_display(status):
    """Retourne l'affichage du statut en français"""
    status_map = {
        OnlineOrderStatus.PENDING: 'En attente de confirmation',
        OnlineOrderStatus.CONFIRMED: 'Confirmée',
        OnlineOrderStatus.PREPARING: 'En préparation',
        OnlineOrderStatus.READY: 'Prête',
        OnlineOrderStatus.OUT_FOR_DELIVERY: 'En livraison',
        OnlineOrderStatus.DELIVERED: 'Livrée',
        OnlineOrderStatus.CANCELLED: 'Annulée'
    }
    return status_map.get(status, status.value)
