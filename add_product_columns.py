#!/usr/bin/env python3
"""
Script pour ajouter les colonnes is_active et is_available aux produits
"""

import os
import sys
import sqlite3

def add_product_columns():
    """Ajoute les colonnes manquantes à la table products"""
    db_path = 'instance/app.db'
    
    if not os.path.exists(db_path):
        print("Base de données non trouvée!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Vérifier quelles colonnes existent déjà
    cursor.execute("PRAGMA table_info(products)")
    existing_columns = [row[1] for row in cursor.fetchall()]
    
    print(f"Colonnes existantes dans products: {len(existing_columns)}")
    
    # Ajouter les colonnes manquantes
    new_columns = [
        ('is_active', 'BOOLEAN', 'TRUE'),
        ('is_available', 'BOOLEAN', 'TRUE')
    ]
    
    for column_name, column_type, default_value in new_columns:
        if column_name not in existing_columns:
            try:
                sql = f"ALTER TABLE products ADD COLUMN {column_name} {column_type} DEFAULT {default_value}"
                print(f"Ajout de la colonne: {column_name}")
                cursor.execute(sql)
            except sqlite3.Error as e:
                print(f"Erreur lors de l'ajout de {column_name}: {e}")
        else:
            print(f"Colonne {column_name} existe déjà")
    
    conn.commit()
    conn.close()
    
    print("Colonnes ajoutées avec succès!")

if __name__ == '__main__':
    add_product_columns()
