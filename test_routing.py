#!/usr/bin/env python3
"""
Script pour tester le routage des sous-domaines
"""

import requests
import sys

def test_url(url, expected_content=None, should_contain=None):
    """Teste une URL et vérifie le contenu"""
    try:
        print(f"\n🔍 Test: {url}")
        response = requests.get(url, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            if should_contain:
                for text in should_contain:
                    if text in content:
                        print(f"   ✅ Contient: '{text}'")
                    else:
                        print(f"   ❌ Ne contient pas: '{text}'")
            print(f"   ✅ Succès")
        else:
            print(f"   ❌ Erreur HTTP {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")

def main():
    """Teste les différentes URLs du système"""
    print("🚀 Test du système de routage")
    print("=" * 50)
    
    # Test 1: POS Principal (doit afficher la landing page du SaaS)
    test_url(
        "http://127.0.0.1:5000",
        should_contain=["Connexion", "Inscription", "POS System"]
    )
    
    # Test 2: Site restaurant avec sous-domaine
    test_url(
        "http://azerty1.lvh.me:5000",
        should_contain=["Restaurant", "Menu", "Commander"]
    )
    
    # Test 3: Menu du restaurant
    test_url(
        "http://azerty1.lvh.me:5000/menu",
        should_contain=["Entrées", "Plats principaux", "Desserts"]
    )
    
    # Test 4: Site all_businesses
    test_url(
        "http://all_businesses.lvh.me:5000",
        should_contain=["Tous les restaurants", "Rechercher"]
    )
    
    # Test 5: Inscription client
    test_url(
        "http://azerty1.lvh.me:5000/customer/register",
        should_contain=["Inscription", "Email", "Mot de passe"]
    )
    
    # Test 6: Connexion client
    test_url(
        "http://azerty1.lvh.me:5000/customer/login",
        should_contain=["Connexion", "Email", "Mot de passe"]
    )
    
    # Test 7: Paramètres POS (doit être accessible depuis le POS principal)
    test_url(
        "http://127.0.0.1:5000/settings/online_ordering",
        should_contain=["Commande en ligne", "Sous-domaine", "Activer"]
    )
    
    print("\n" + "=" * 50)
    print("🏁 Tests terminés")

if __name__ == '__main__':
    main()
