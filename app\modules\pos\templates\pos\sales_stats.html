{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
.chart-box {
    width: 100%;
    min-height: 400px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">
        <i class="fas fa-chart-line"></i> {{ title }}
    </h2>

    <!-- Sélecteur de période -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="btn-group">
                        <a href="{{ url_for('pos.sales_stats', period='today') }}" 
                           class="btn btn-outline-primary {% if period == 'today' %}active{% endif %}">
                            Aujourd'hui 
                        </a>
                        <a href="{{ url_for('pos.sales_stats', period='week') }}" 
                           class="btn btn-outline-primary {% if period == 'week' %}active{% endif %}">
                            7 derniers jours 
                        </a>
                        <a href="{{ url_for('pos.sales_stats', period='month') }}" 
                           class="btn btn-outline-primary {% if period == 'month' %}active{% endif %}">
                            30 derniers jours
                        </a>
                        <a href="{{ url_for('pos.sales_stats', period='year') }}" 
                           class="btn btn-outline-primary {% if period == 'year' %}active{% endif %}">
                            12 derniers mois
                        </a>
                        <a href="{{ url_for('pos.sales_stats', period='custom') }}" 
                           class="btn btn-outline-primary {% if period == 'custom' %}active{% endif %}">
                            Personnalisé 
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <form method="GET" class="row g-3 align-items-center {% if period != 'custom' %}d-none{% endif %}" id="dateForm">
                        <input type="hidden" name="period" value="custom">
                        <div class="col-auto">
                            <label class="form-label">Du</label>
                            <input type="date" class="form-control" name="start_date" 
                                   value="{{ request.args.get('start_date', start_date.strftime('%Y-%m-%d')) }}">
                        </div>
                        <div class="col-auto">
                            <label class="form-label">Au</label>
                            <input type="date" class="form-control" name="end_date" 
                                   value="{{ request.args.get('end_date', end_date.strftime('%Y-%m-%d')) }}">
                        </div>
                        <div class="col-auto">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">Filtrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques générales -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6 class="card-title">Nombre de ventes</h6>
                    <h2 class="mb-0">{{ total_sales }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6 class="card-title">Chiffre d'affaires</h6>
                    <h2 class="mb-0">{{ "%.2f"|format(total_revenue) }} €</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6 class="card-title">Panier moyen</h6>
                    <h2 class="mb-0">{{ "%.2f"|format(average_sale) }} €</h2>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Graphique des ventes -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Évolution des ventes</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top produits -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Top 10 des produits</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for product, quantity, revenue in top_products %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ product.name }}</h6>
                                <small>{{ quantity }} vendus</small>
                            </div>
                            <p class="mb-1">CA: {{ "%.2f"|format(revenue) }} €</p>
                        </div>
                        {% else %}
                        <div class="list-group-item">
                            Aucune donnée disponible
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
console.log('Loading sales stats script...');

// Vérifier si Chart.js est chargé
if (typeof Chart === 'undefined') {
    console.error('Chart.js is not loaded!');
} else {
    console.log('Chart.js is loaded successfully');
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');
    
    const canvas = document.getElementById('salesChart');
    if (!canvas) {
        console.error('Canvas not found');
        return;
    }
    console.log('Canvas found:', canvas);

    let labels = [];
    let values = [];
    const period = '{{ period }}';
    console.log('Period:', period);

    // Log raw data
    console.log('Period:', period);
    
    {% if hourly_sales is defined %}
    console.log('Hourly sales:', {{ hourly_sales|tojson|safe }});
    {% else %}
    console.log('Hourly sales: not defined');
    const hourly_sales = {};
    {% endif %}
    
    {% if weekly_sales is defined %}
    console.log('Weekly sales:', {{ weekly_sales|tojson|safe }});
    {% else %}
    console.log('Weekly sales: not defined');
    const weekly_sales = [];
    {% endif %}
    
    {% if monthly_sales is defined %}
    console.log('Monthly sales:', {{ monthly_sales|tojson|safe }});
    {% else %}
    console.log('Monthly sales: not defined');
    const monthly_sales = [];
    {% endif %}
    
    {% if yearly_sales is defined %}
    console.log('Yearly sales:', {{ yearly_sales|tojson|safe }});
    {% else %}
    console.log('Yearly sales: not defined');
    const yearly_sales = [];
    {% endif %}
    
    {% if sales_by_day is defined %}
    console.log('Sales by day:', {{ sales_by_day|tojson|safe }});
    {% else %}
    console.log('Sales by day: not defined');
    const sales_by_day = {};
    {% endif %}

    // Récupérer les données selon la période
    try {
        if (period === 'today') {
            {% if hourly_sales is defined %}
            const data = {{ hourly_sales|tojson|safe }};
            {% else %}
            const data = {};
            {% endif %}
            labels = Object.keys(data).map(hour => `${hour}h`);
            values = Object.values(data);
        } else if (period === 'week') {
            {% if weekly_sales is defined %}
            const data = {{ weekly_sales|tojson|safe }};
            {% else %}
            const data = [];
            {% endif %}
            labels = data.map(item => item[0]);
            values = data.map(item => item[1]);
        } else if (period === 'month') {
            {% if monthly_sales is defined %}
            const data = {{ monthly_sales|tojson|safe }};
            {% else %}
            const data = [];
            {% endif %}
            labels = data.map(item => item[0]);
            values = data.map(item => item[1]);
        } else if (period === 'year') {
            {% if yearly_sales is defined %}
            const data = {{ yearly_sales|tojson|safe }};
            {% else %}
            const data = [];
            {% endif %}
            labels = data.map(item => item[0]);
            values = data.map(item => item[1]);
        } else {
            // Custom period - use sales by day
            {% if sales_by_day is defined %}
            const data = {{ sales_by_day|tojson|safe }};
            {% else %}
            const data = {};
            {% endif %}
            labels = Object.keys(data);
            values = Object.values(data);
        }

        console.log('Processed data:', { labels, values });

        // Créer le graphique
        const chart = new Chart(canvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Chiffre d\'affaires (€)',
                    data: values,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y.toFixed(2) + ' €';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toFixed(2) + ' €';
                            }
                        }
                    }
                }
            }
        });
        console.log('Chart created successfully');
    } catch (error) {
        console.error('Error creating chart:', error);
    }
});

// Gestion du formulaire de dates personnalisées
document.addEventListener('DOMContentLoaded', function() {
    const dateForm = document.getElementById('dateForm');
    const periodButtons = document.querySelectorAll('.btn-group .btn');
    
    periodButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (this.href.includes('period=custom')) {
                e.preventDefault();
                dateForm.classList.remove('d-none');
            } else {
                dateForm.classList.add('d-none');
            }
        });
    });
});
</script>
{% endblock %} 