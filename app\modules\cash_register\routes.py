from flask import render_template, request, jsonify, current_app, url_for, send_file, flash, redirect
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app.modules.cash_register.models_cash_register import (
    CashRegister, CashOperation, CashRegisterOperationType,
    CashRegisterSettings, PaymentMethod
)
from app.modules.auth.models import User
from app.modules.inventory.models_supplier import Supplier
from app.extensions import db
from app.utils.decorators import permission_required
from app.modules.cash_register.forms import (
    OpenRegisterForm, CloseRegisterForm, CashInForm,
    CashOutForm, BankDepositForm, ResetRegisterForm, CashRegisterSettingsForm
)
from datetime import datetime, timedelta
import os

from . import bp

@bp.route('/')
@login_required
@permission_required('can_access_cash_register')
def index():
    try:
        current_app.logger.debug("Starting index route")
        current_register = CashRegister.get_current(current_user.id)
        settings = CashRegisterSettings.get_settings(current_user.id)
        current_app.logger.debug(f"Current register: {current_register}")
        
        # Récupérer les paramètres de pagination
        movements_page = request.args.get('movements_page', 1, type=int)
        register_ops_page = request.args.get('register_ops_page', 1, type=int)
        per_page = 10  # Nombre d'éléments par page
        
        # Récupérer le dernier montant de fermeture
        last_closing_amount = None
        if current_register:
            last_closing = CashOperation.query.filter(
                CashOperation.register_id == current_register.id,
                CashOperation.type == CashRegisterOperationType.CLOSING
            ).order_by(CashOperation.date.desc()).first()
            
            if last_closing:
                last_closing_amount = last_closing.final_amount
        
        if not current_register:
            current_app.logger.debug("Creating new register")
            current_register = CashRegister(
                owner_id=current_user.id,
                is_open=False,
                float_amount=0,
                current_balance=0
            )
            db.session.add(current_register)
            db.session.commit()
        
        operations = []
        register_operations = []
        total_sales = total_deposits = total_withdrawals = 0
        cash_total = card_total = check_total = other_total = 0
        
        if current_register.is_open:
            current_app.logger.debug("Register is open, getting operations")
            # Récupérer toutes les opérations depuis la dernière ouverture avec pagination
            operations = CashOperation.query.filter(
                CashOperation.register_id == current_register.id,
                CashOperation.date >= current_register.last_opened_at,
                CashOperation.type.notin_([CashRegisterOperationType.OPENING, CashRegisterOperationType.CLOSING])
            ).order_by(CashOperation.date.desc()).paginate(
                page=movements_page,
                per_page=per_page,
                error_out=False
            )
            
            # Récupérer l'opération d'ouverture pour le fond initial
            opening_operation = CashOperation.query.filter(
                CashOperation.register_id == current_register.id,
                CashOperation.type == CashRegisterOperationType.OPENING,
                CashOperation.date >= current_register.last_opened_at
            ).order_by(CashOperation.date.desc()).first()
            
            if opening_operation:
                current_register.float_amount = opening_operation.initial_amount
            
            # Calculer les totaux par type d'opération et mode de paiement
            all_operations = CashOperation.query.filter(
                CashOperation.register_id == current_register.id,
                CashOperation.date >= current_register.last_opened_at
            ).all()
            
            for op in all_operations:
                current_app.logger.debug(f"Processing operation: {op.type}, amount: {op.amount}, payment_method: {op.payment_method}")
                
                if op.type == CashRegisterOperationType.SALE:
                    total_sales += op.amount
                    # Ajouter au total du mode de paiement correspondant
                    if op.payment_method and op.payment_method.value == 'CASH':
                        cash_total += op.amount
                        current_app.logger.debug(f"Added {op.amount} to cash_total")
                    elif op.payment_method and op.payment_method.value == 'CARD':
                        card_total += op.amount
                        current_app.logger.debug(f"Added {op.amount} to card_total")
                    elif op.payment_method and op.payment_method.value == 'CHECK':
                        check_total += op.amount
                        current_app.logger.debug(f"Added {op.amount} to check_total")
                    else:
                        other_total += op.amount
                        current_app.logger.debug(f"Added {op.amount} to other_total")
                elif op.type == CashRegisterOperationType.CASH_IN:
                    total_deposits += op.amount
                    if op.payment_method and op.payment_method.value == 'CASH':
                        cash_total += op.amount
                    elif op.payment_method and op.payment_method.value == 'CARD':
                        card_total += op.amount
                    elif op.payment_method and op.payment_method.value == 'CHECK':
                        check_total += op.amount
                    else:
                        other_total += op.amount
                elif op.type == CashRegisterOperationType.CASH_OUT:
                    total_withdrawals += abs(op.amount)
            
            # Récupérer les opérations d'ouverture/fermeture avec pagination
            register_operations = CashOperation.query.filter(
                CashOperation.register_id == current_register.id,
                CashOperation.type.in_([CashRegisterOperationType.OPENING, CashRegisterOperationType.CLOSING])
            ).order_by(CashOperation.date.desc()).paginate(
                page=register_ops_page,
                per_page=per_page,
                error_out=False
            )
        
        current_app.logger.debug(f"Float amount: {current_register.float_amount}")
        current_app.logger.debug(f"Total sales: {total_sales}")
        current_app.logger.debug(f"Total deposits: {total_deposits}")
        current_app.logger.debug(f"Total withdrawals: {total_withdrawals}")
        
        # Créer les formulaires
        register_form = OpenRegisterForm()
        close_form = CloseRegisterForm()
        cash_in_form = CashInForm()
        cash_out_form = CashOutForm()
        bank_deposit_form = BankDepositForm()
        reset_form = ResetRegisterForm()
        
        return render_template('cash_register/index.html',
                             current_register=current_register,
                             total_sales=total_sales,
                             total_deposits=total_deposits,
                             total_withdrawals=total_withdrawals,
                             cash_total=cash_total,
                             card_total=card_total,
                             check_total=check_total,
                             other_total=other_total,
                             movements=operations,
                             register_operations=register_operations,
                             register_form=register_form,
                             close_form=close_form,
                             cash_in_form=cash_in_form,
                             cash_out_form=cash_out_form,
                             bank_deposit_form=bank_deposit_form,
                             reset_form=reset_form,
                             last_closing_amount=last_closing_amount,
                             settings=settings,
                             CashRegisterOperationType=CashRegisterOperationType)
                             
    except Exception as e:
        current_app.logger.error(f"Error in index route: {str(e)}")
        current_app.logger.exception(e)
        raise

@bp.route('/open', methods=['POST'])
@login_required
@permission_required('can_manage_cash_register')
def open_register():
    try:
        form = OpenRegisterForm()
        if form.validate_on_submit():
            register = CashRegister.get_current(current_user.id)
            settings = CashRegisterSettings.get_settings(current_user.id)
            
            if register:
                if register.is_open:
                    flash('La caisse est déjà ouverte', 'error')
                    return redirect(url_for('cash_register.index'))
                
                # Récupérer le montant de la dernière fermeture
                last_closing = CashOperation.query.filter(
                    CashOperation.register_id == register.id,
                    CashOperation.type == CashRegisterOperationType.CLOSING
                ).order_by(CashOperation.date.desc()).first()
                
                initial_amount = form.initial_amount.data
                if form.use_last_amount.data and last_closing:
                    initial_amount = last_closing.final_amount
                
                # Vérifier les contraintes selon les paramètres
                if settings.minimum_amount_required and initial_amount < settings.minimum_amount:
                    flash(f'Le montant initial doit être au moins de {settings.minimum_amount}€', 'error')
                    return redirect(url_for('cash_register.index'))
                
                # Si le montant est différent du dernier montant de fermeture et que l'option est activée
                if settings.use_last_closing_amount and last_closing and \
                   abs(initial_amount - last_closing.final_amount) > 0.01 and \
                   not form.reason.data:
                    flash('Un motif est requis lorsque le montant diffère du dernier montant de fermeture', 'error')
                    return redirect(url_for('cash_register.index'))
                
                try:
                    # Mettre à jour le fond de caisse et le solde initial
                    register.float_amount = initial_amount
                    register.current_balance = initial_amount
                    register.is_open = True
                    register.last_opened_at = datetime.utcnow()
                    
                    # Créer l'opération d'ouverture
                    note = f"Ouverture avec {initial_amount}€"
                    if form.reason.data:
                        note += f" - Motif: {form.reason.data}"
                        
                    operation = CashOperation(
                        register_id=register.id,
                        type=CashRegisterOperationType.OPENING,
                        amount=initial_amount,
                        initial_amount=initial_amount,
                        user_id=current_user.id,
                        owner_id=register.owner_id,
                        note=note
                    )
                    
                    db.session.add(operation)
                    db.session.commit()
                    
                    flash('Caisse ouverte avec succès', 'success')
                except Exception as e:
                    db.session.rollback()
                    current_app.logger.error(f"Erreur lors de l'ouverture de la caisse: {str(e)}")
                    flash('Erreur lors de l\'ouverture de la caisse', 'error')
                
                return redirect(url_for('cash_register.index'))
            else:
                flash('Aucune caisse trouvée', 'error')
                return redirect(url_for('cash_register.index'))
        
        # Si la validation du formulaire échoue, retourner les erreurs
        for field, field_errors in form.errors.items():
            for error in field_errors:
                flash(f"{form[field].label.text}: {error}", 'error')
                current_app.logger.error(f"Form validation error - {field}: {error}")
        
        return redirect(url_for('cash_register.index'))
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error opening register: {str(e)}")
        flash(f'Erreur lors de l\'ouverture de la caisse: {str(e)}', 'error')
        return redirect(url_for('cash_register.index'))

@bp.route('/close', methods=['POST'])
@login_required
@permission_required('can_manage_cash_register')
def close_register():
    try:
        current_app.logger.debug("Starting close_register route")
        form = CloseRegisterForm()
        
        if form.validate_on_submit():
            current_app.logger.debug(f"Form data: final_amount={form.final_amount.data}, note={form.note.data}")
            register = CashRegister.get_current(current_user.id)
            
            if not register:
                current_app.logger.error("No register found")
                flash('Aucune caisse trouvée', 'error')
                return redirect(url_for('cash_register.index'))
            
            if not register.is_open:
                current_app.logger.error("Register is already closed")
                flash('La caisse est déjà fermée', 'error')
                return redirect(url_for('cash_register.index'))
            
            # Créer une opération de fermeture
            try:
                operation = CashOperation(
                    register_id=register.id,
                    type=CashRegisterOperationType.CLOSING,
                    amount=form.final_amount.data,
                    initial_amount=register.float_amount,
                    final_amount=form.final_amount.data,
                    user_id=current_user.id,
                    owner_id=register.owner_id,
                    note=form.note.data or "Fermeture de caisse"
                )
                
                register.is_open = False
                register.last_closed_at = datetime.utcnow()
                register.current_balance = form.final_amount.data
                
                db.session.add(operation)
                db.session.commit()
                
                flash('Caisse fermée avec succès', 'success')
                return redirect(url_for('cash_register.index'))
                
            except Exception as e:
                current_app.logger.error(f"Error creating closing operation: {str(e)}")
                db.session.rollback()
                flash(f'Erreur lors de la fermeture de la caisse: {str(e)}', 'error')
                return redirect(url_for('cash_register.index'))
        
        # Si la validation du formulaire échoue
        for field, field_errors in form.errors.items():
            for error in field_errors:
                flash(f"{form[field].label.text}: {error}", 'error')
        return redirect(url_for('cash_register.index'))
        
    except Exception as e:
        current_app.logger.error(f"Error in close_register: {str(e)}")
        flash(f'Erreur lors de la fermeture de la caisse: {str(e)}', 'error')
        return redirect(url_for('cash_register.index'))

@bp.route('/history')
@login_required
@permission_required('can_access_cash_register')
def history():
    # Récupérer les paramètres de filtrage et pagination
    page = request.args.get('page', 1, type=int)
    per_page = 20  # Nombre d'opérations par page
    start_date = request.args.get('start_date', 
                                (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))
    operation_type = request.args.get('operation_type')

    # Convertir les dates en objets datetime avec les heures de début et fin de journée
    start_datetime = datetime.strptime(start_date, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
    end_datetime = datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59)

    # Construire la requête de base
    query = CashOperation.query.filter(
        CashOperation.owner_id == current_user.id
    )

    # Appliquer les filtres
    if start_date:
        query = query.filter(CashOperation.date >= start_datetime)
    if end_date:
        query = query.filter(CashOperation.date <= end_datetime)
    if operation_type:
        query = query.filter(CashOperation.type == operation_type)

    # Récupérer les opérations avec pagination
    operations = query.order_by(CashOperation.date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Calculer les totaux pour le résumé (sur toutes les opérations)
    all_operations = query.all()
    total_in = sum(op.amount for op in all_operations if op.amount > 0)
    total_out = abs(sum(op.amount for op in all_operations if op.amount < 0))
    balance = sum(op.amount for op in all_operations)

    summary = {
        'total_in': total_in,
        'total_out': total_out,
        'balance': balance,
        'total_operations': len(all_operations)
    }

    return render_template('cash_register/history.html',
                         operations=operations,
                         operation_types=CashRegisterOperationType,
                         start_date=start_date,
                         end_date=end_date,
                         selected_type=operation_type,
                         summary=summary,
                         CashRegisterOperationType=CashRegisterOperationType)

@bp.route('/cash-in', methods=['POST'])
@login_required
@permission_required('can_manage_cash_register')
def cash_in():
    form = CashInForm()
    if form.validate_on_submit():
        register = CashRegister.get_current(current_user.id)
        if register and register.is_open:
            operation = CashOperation(
                register_id=register.id,
                type=CashRegisterOperationType.CASH_IN,
                amount=form.amount.data,
                payment_method=form.source.data,
                note=form.note.data,
                user_id=current_user.id,
                owner_id=register.owner_id
            )
            register.current_balance += form.amount.data
            
            db.session.add(operation)
            db.session.commit()
            
            flash('Entrée de caisse enregistrée avec succès', 'success')
        else:
            flash('La caisse doit être ouverte pour effectuer cette opération', 'error')
    else:
        flash('Formulaire invalide', 'error')
    
    return redirect(url_for('cash_register.index'))

@bp.route('/cash-out', methods=['POST'])
@login_required
@permission_required('can_manage_cash_register')
def cash_out():
    form = CashOutForm()
    if form.validate_on_submit():
        register = CashRegister.get_current(current_user.id)
        if register and register.is_open:
            if form.amount.data > register.current_balance:
                flash('Montant supérieur au solde en caisse', 'error')
                return redirect(url_for('cash_register.index'))
                
            operation = CashOperation(
                register_id=register.id,
                type=CashRegisterOperationType.CASH_OUT,
                amount=-form.amount.data,  # Montant négatif pour une sortie
                note=form.note.data,
                reason=form.reason.data,
                user_id=current_user.id,
                owner_id=register.owner_id
            )
            register.current_balance -= form.amount.data
            
            db.session.add(operation)
            db.session.commit()
            
            flash('Sortie de caisse enregistrée avec succès', 'success')
        else:
            flash('La caisse doit être ouverte pour effectuer cette opération', 'error')
    else:
        flash('Formulaire invalide', 'error')
    
    return redirect(url_for('cash_register.index')) 

@bp.route('/bank-deposit', methods=['POST'])
@login_required
@permission_required('can_manage_cash_register')
def bank_deposit():
    form = BankDepositForm()
    if form.validate_on_submit():
        register = CashRegister.get_current(current_user.id)
        if register and register.is_open:
            if form.amount.data > register.current_balance:
                flash('Montant supérieur au solde en caisse', 'error')
                return redirect(url_for('cash_register.index'))
                
            operation = CashOperation(
                register_id=register.id,
                type=CashRegisterOperationType.BANK_DEPOSIT,
                amount=-form.amount.data,  # Montant négatif car c'est une sortie
                note=form.note.data,
                user_id=current_user.id,
                owner_id=register.owner_id
            )
            register.current_balance -= form.amount.data
            
            # Gérer le fichier de justificatif si présent
            if form.receipt.data:
                filename = secure_filename(form.receipt.data.filename)
                filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], 'bank_deposits', filename)
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                form.receipt.data.save(filepath)
                operation.receipt_path = filepath
            
            db.session.add(operation)
            db.session.commit()
            
            flash('Versement en banque enregistré avec succès', 'success')
        else:
            flash('La caisse doit être ouverte pour effectuer cette opération', 'error')
    else:
        flash('Formulaire invalide', 'error')
    
    return redirect(url_for('cash_register.index')) 

@bp.route('/reset', methods=['POST'])
@login_required
@permission_required('can_manage_cash_register')
def reset_register():
    """Réinitialise la caisse"""
    try:
        register = CashRegister.get_current(current_user.id)
        if register:
            # Créer une opération de fermeture si la caisse est ouverte
            if register.is_open:
                operation = CashOperation(
                    register_id=register.id,
                    type=CashRegisterOperationType.CLOSING,
                    amount=register.current_balance,
                    user_id=current_user.id,
                    owner_id=register.owner_id,
                    note="Réinitialisation de la caisse"
                )
                db.session.add(operation)

            # Réinitialiser la caisse
            register.is_open = False
            register.float_amount = 0
            register.current_balance = 0
            register.last_opened_at = None
            
            db.session.commit()
            flash('La caisse a été réinitialisée avec succès', 'success')
        else:
            flash('Aucune caisse trouvée', 'error')
            
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la réinitialisation de la caisse: {str(e)}")
        db.session.rollback()
        flash('Une erreur est survenue lors de la réinitialisation de la caisse', 'error')
    
    return redirect(url_for('cash_register.index'))

@bp.route('/settings', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_cash_register')
def settings():
    try:
        settings = CashRegisterSettings.get_settings(current_user.id)
        form = CashRegisterSettingsForm()
        
        if request.method == 'GET':
            form.minimum_amount_required.data = settings.minimum_amount_required
            form.minimum_amount.data = settings.minimum_amount
            form.use_last_closing_amount.data = settings.use_last_closing_amount
        
        elif form.validate_on_submit():
            settings.minimum_amount_required = form.minimum_amount_required.data
            settings.minimum_amount = form.minimum_amount.data
            settings.use_last_closing_amount = form.use_last_closing_amount.data
            
            try:
                db.session.commit()
                flash('Paramètres mis à jour avec succès', 'success')
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Erreur lors de la mise à jour des paramètres: {str(e)}")
                flash('Erreur lors de la mise à jour des paramètres', 'error')
            
            return redirect(url_for('cash_register.settings'))
        
        return render_template('cash_register/settings.html', form=form, settings=settings)
        
    except Exception as e:
        current_app.logger.error(f"Erreur dans la route settings: {str(e)}")
        flash('Une erreur est survenue', 'error')
        return redirect(url_for('cash_register.index'))