#!/usr/bin/env python3
"""
Script de test pour vérifier que les stocks se mettent à jour correctement
lors des commandes en ligne.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.inventory.models_recipe import Recipe, RecipeItem

def test_stock_updates():
    """Test la mise à jour des stocks pour différents types de produits"""
    
    app = create_app()
    with app.app_context():
        print("=== Test de mise à jour des stocks pour les commandes en ligne ===\n")
        
        # 1. Tester avec un produit simple (sans recette)
        print("1. Test avec un produit simple (sans recette)")
        simple_product = Product.query.filter_by(has_recipe=False).first()
        if simple_product:
            print(f"   Produit: {simple_product.name}")
            print(f"   Stock avant: {simple_product.stock_quantity}")
            
            # Simuler une mise à jour de stock
            initial_stock = simple_product.stock_quantity
            test_quantity = 2
            
            if initial_stock >= test_quantity:
                success = simple_product.update_stock(
                    test_quantity, 
                    operation='subtract', 
                    reason='SALE', 
                    reference='TEST_ORDER_001',
                    notes='Test de commande en ligne'
                )
                
                if success:
                    print(f"   Stock après déduction de {test_quantity}: {simple_product.stock_quantity}")
                    print(f"   ✅ Mise à jour réussie")
                    
                    # Remettre le stock comme avant
                    simple_product.update_stock(test_quantity, operation='add', reason='ADJUSTMENT', reference='TEST_RESTORE')
                    print(f"   Stock restauré: {simple_product.stock_quantity}")
                else:
                    print(f"   ❌ Échec de la mise à jour")
            else:
                print(f"   ⚠️  Stock insuffisant pour le test ({initial_stock} < {test_quantity})")
        else:
            print("   ⚠️  Aucun produit simple trouvé")
        
        print()
        
        # 2. Tester avec un produit avec recette
        print("2. Test avec un produit avec recette")
        recipe_product = Product.query.filter_by(has_recipe=True).first()
        if recipe_product and recipe_product.recipe:
            print(f"   Produit: {recipe_product.name}")
            print(f"   Ingrédients de la recette:")
            
            # Afficher les ingrédients et leurs stocks
            for recipe_item in recipe_product.recipe.items:
                ingredient = recipe_item.ingredient
                print(f"     - {ingredient.name}: {recipe_item.quantity} {ingredient.unit} (stock: {ingredient.stock_quantity})")
            
            # Calculer la quantité possible
            possible_quantity = recipe_product.recipe.get_possible_quantity()
            print(f"   Quantité possible à produire: {possible_quantity}")
            
            if possible_quantity >= 1:
                # Sauvegarder les stocks initiaux des ingrédients
                initial_stocks = {}
                for recipe_item in recipe_product.recipe.items:
                    initial_stocks[recipe_item.ingredient.id] = recipe_item.ingredient.stock_quantity
                
                # Tester la mise à jour
                test_quantity = 1
                success = recipe_product.update_stock(
                    test_quantity, 
                    operation='subtract', 
                    reason='SALE', 
                    reference='TEST_ORDER_002',
                    notes='Test de commande en ligne avec recette'
                )
                
                if success:
                    print(f"   ✅ Mise à jour réussie pour {test_quantity} unité(s)")
                    print(f"   Stocks des ingrédients après:")
                    
                    for recipe_item in recipe_product.recipe.items:
                        ingredient = recipe_item.ingredient
                        db.session.refresh(ingredient)  # Recharger depuis la DB
                        print(f"     - {ingredient.name}: {ingredient.stock_quantity} {ingredient.unit}")
                    
                    # Restaurer les stocks
                    for recipe_item in recipe_product.recipe.items:
                        ingredient = recipe_item.ingredient
                        original_stock = initial_stocks[ingredient.id]
                        ingredient.stock_quantity = original_stock
                    
                    db.session.commit()
                    print(f"   Stocks restaurés")
                else:
                    print(f"   ❌ Échec de la mise à jour")
            else:
                print(f"   ⚠️  Aucune quantité possible à produire")
        else:
            print("   ⚠️  Aucun produit avec recette trouvé")
        
        print()
        
        # 3. Afficher un résumé des produits disponibles
        print("3. Résumé des produits disponibles:")
        all_products = Product.query.limit(5).all()
        for product in all_products:
            if product.has_recipe:
                possible = product.recipe.get_possible_quantity() if product.recipe else 0
                print(f"   - {product.name} (avec recette): {possible} unités possibles")
            else:
                print(f"   - {product.name} (simple): {product.stock_quantity} en stock")
        
        print("\n=== Test terminé ===")

if __name__ == "__main__":
    test_stock_updates()
