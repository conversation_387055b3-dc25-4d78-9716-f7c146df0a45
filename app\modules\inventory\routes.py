from flask import render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.extensions import db
from app.utils.decorators import inventory_access_required as permission_required
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.inventory.models_recipe import Recipe, RecipeItem
from app.modules.inventory.models_ingredient import Ingredient, IngredientCategory
from app.modules.inventory.models_supplier import Supplier
from app.utils.files import save_image, save_product_image
from sqlalchemy import or_
from . import bp
from .forms import (
    ProductForm, ProductCategoryForm, RecipeForm, RecipeItemForm, 
    IngredientForm, IngredientStockForm, IngredientCategoryForm,
    SupplierForm
)

# Gestion des catégories d'ingrédients
from .ingredient_categories import (
    ingredient_categories, add_ingredient_category, 
    edit_ingredient_category, delete_ingredient_category
)

# Gestion des fournisseurs
from .routes_suppliers import (
    suppliers, add_supplier, edit_supplier, delete_supplier
) 
import os

@bp.route('/product-categories')
@login_required
@permission_required
def product_categories():
    """Liste des catégories de produits"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    
    query = ProductCategory.query.filter_by(owner_id=current_user.id)
    
    if search:
        query = query.filter(ProductCategory.name.ilike(f'%{search}%'))
    
    categories = query.order_by(ProductCategory.name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Calculer les statistiques pour chaque catégorie
    stats = {}
    for category in categories.items:
        total_products = Product.query.filter_by(
            category_id=category.id,
            owner_id=current_user.id
        ).count()
        
        # Pour l'instant, nous considérons tous les produits comme actifs
        # puisque nous n'avons pas de champ is_active
        active_products = total_products
        
        stats[category.id] = {
            'total_products': total_products,
            'active_products': active_products
        }
    
    return render_template('inventory/product_categories.html',
                         title='Catégories de produits',
                         categories=categories,
                         search=search,
                         per_page=per_page,
                         stats=stats)

@bp.route('/product-categories/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_product_category():
    """Ajouter une catégorie de produits"""
    form = ProductCategoryForm()
    
    if form.validate_on_submit():
        category = ProductCategory(
            name=form.name.data,
            description=form.description.data,
            color=form.color.data,
            owner_id=current_user.id
        )
        
        if form.image.data:
            try:
                image_path = save_image(form.image.data, 'categories')
                if image_path:
                    category.image_path = image_path
            except Exception as e:
                flash(f"Erreur lors de l'upload de l'image: {str(e)}", "error")
        
        db.session.add(category)
        db.session.commit()
        flash('Catégorie ajoutée avec succès!', 'success')
        return redirect(url_for('inventory.product_categories'))
    
    return render_template('inventory/product_category_form.html',
                         title='Ajouter une catégorie',
                         form=form)

@bp.route('/product-categories/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_product_category(id):
    """Modifier une catégorie de produits"""
    category = ProductCategory.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = ProductCategoryForm(obj=category)
    
    if form.validate_on_submit():
        category.name = form.name.data
        category.description = form.description.data
        category.color = form.color.data
        
        if form.image.data:
            try:
                # Supprimer l'ancienne image si elle existe
                if category.image_path:
                    old_image_path = os.path.join('app', 'static', category.image_path)
                    if os.path.exists(old_image_path):
                        os.remove(old_image_path)
                
                # Sauvegarder la nouvelle image
                image_path = save_image(form.image.data, 'categories')
                if image_path:
                    category.image_path = image_path
                else:
                    flash("Erreur lors de l'upload de l'image. Les autres modifications ont été sauvegardées.", "warning")
            except Exception as e:
                flash(f"Erreur lors de l'upload de l'image: {str(e)}", "error")
        
        db.session.commit()
        flash('Catégorie modifiée avec succès!', 'success')
        return redirect(url_for('inventory.product_categories'))
    
    return render_template('inventory/product_category_form.html',
                         title='Modifier une catégorie',
                         form=form,
                         category=category)

@bp.route('/product-categories/<int:id>/delete', methods=['POST'])
@login_required
@permission_required
def delete_product_category(id):
    """Supprimer une catégorie de produits"""
    category = ProductCategory.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    # Check if category has products
    if category.products.count() > 0:
        flash('Impossible de supprimer cette catégorie car elle contient des produits.', 'error')
        return redirect(url_for('inventory.product_categories'))
    
    try:
        db.session.delete(category)
        db.session.commit()
        flash('Catégorie supprimée avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la suppression: {str(e)}', 'error')
    
    return redirect(url_for('inventory.product_categories'))

@bp.route('/product-categories/<int:category_id>/delete-image', methods=['POST'])
@login_required
@permission_required
def delete_product_category_image(category_id):
    """Supprimer l'image d'une catégorie de produits"""
    category = ProductCategory.query.filter_by(id=category_id, owner_id=current_user.id).first_or_404()
    
    if not category.image_path:
        flash("Cette catégorie n'a pas d'image.", "warning")
        return redirect(url_for('inventory.edit_product_category', id=category_id))
    
    try:
        # Supprimer le fichier
        image_path = os.path.join('app', 'static', category.image_path)
        if os.path.exists(image_path):
            os.remove(image_path)
        
        # Mettre à jour la base de données
        category.image_path = None
        db.session.commit()
        flash("Image supprimée avec succès!", "success")
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de la suppression de l'image: {str(e)}", "error")
    
    return redirect(url_for('inventory.edit_product_category', id=category_id))

@bp.route('/products')
@login_required
@permission_required
def products():
    """Liste des produits"""
    # Paramètres de pagination et filtrage
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category', type=int)
    
    # Construction de la requête de base
    query = Product.query.filter_by(owner_id=current_user.id)
    
    # Appliquer les filtres
    if search:
        query = query.filter(Product.name.ilike(f'%{search}%'))
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    # Récupérer les produits avec pagination
    products = query.order_by(Product.name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Récupérer toutes les catégories pour le filtre
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()
    
    return render_template('inventory/products.html',
                         title='Produits',
                         products=products,
                         categories=categories,
                         search=search,
                         selected_category=category_id,
                         per_page=per_page)

@bp.route('/products/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_product():
    """Ajouter un produit"""
    form = ProductForm()
    # Populate category choices
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()
    form.category_id.choices = [(c.id, c.name) for c in categories]
    
    if form.validate_on_submit():
        # Créer d'abord le produit sans image
        product = Product(
            name=form.name.data,
            description=form.description.data,
            category_id=form.category_id.data,
            price=form.price.data,
            cost_price=form.cost_price.data,
            stock_quantity=form.stock_quantity.data if not form.has_recipe.data else 0,
            minimum_stock=form.minimum_stock.data,
            unit=form.unit.data,
            has_recipe=form.has_recipe.data,
            owner_id=current_user.id
        )
        
        # Ajouter le produit pour obtenir son ID
        db.session.add(product)
        db.session.commit()
        
        # Maintenant gérer l'image si elle est fournie
        if form.image.data:
            try:
                image_path = save_product_image(form.image.data, product.id)
                if image_path:
                    product.image_path = image_path
                    db.session.commit()
                else:
                    flash("Erreur lors de l'upload de l'image. Le produit a été créé sans image.", "warning")
            except Exception as e:
                flash(f"Erreur lors de l'upload de l'image: {str(e)}", "error")
        
        flash('Produit ajouté avec succès!', 'success')
        
        # Rediriger vers la page de recette si has_recipe est coché
        if form.has_recipe.data:
            return redirect(url_for('inventory.recipe', product_id=product.id))
        return redirect(url_for('inventory.products'))
    
    return render_template('inventory/product_form.html',
                         title='Ajouter un produit',
                         form=form)

@bp.route('/products/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_product(id):
    """Modifier un produit"""
    product = Product.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = ProductForm(obj=product)
    
    # Populate category choices
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()
    form.category_id.choices = [(c.id, c.name) for c in categories]
    
    if form.validate_on_submit():
        # Mettre à jour les champs du produit
        product.name = form.name.data
        product.description = form.description.data
        product.category_id = form.category_id.data
        product.price = form.price.data
        product.cost_price = form.cost_price.data
        if not form.has_recipe.data:
            product.stock_quantity = form.stock_quantity.data
        product.minimum_stock = form.minimum_stock.data
        product.unit = form.unit.data
        product.has_recipe = form.has_recipe.data
        
        # Gérer l'upload d'image si une nouvelle image est fournie
        if form.image.data:
            try:
                # Sauvegarder la nouvelle image
                image_path = save_product_image(form.image.data, product.id)
                if image_path:
                    # Supprimer l'ancienne image si elle existe
                    if product.image_path:
                        old_image_path = os.path.join('app', 'static', product.image_path)
                        old_thumb_path = os.path.join('app', 'static', 'uploads', 'products', f"thumb_{os.path.basename(product.image_path)}")
                        try:
                            if os.path.exists(old_image_path):
                                os.remove(old_image_path)
                            if os.path.exists(old_thumb_path):
                                os.remove(old_thumb_path)
                        except Exception as e:
                            current_app.logger.error(f"Erreur lors de la suppression de l'ancienne image: {str(e)}")
                    
                    # Mettre à jour le chemin de l'image
                    product.image_path = image_path
                else:
                    flash("Erreur lors de l'upload de l'image. Les autres modifications ont été sauvegardées.", "warning")
            except Exception as e:
                flash(f"Erreur lors de l'upload de l'image: {str(e)}", "error")
        
        try:
            db.session.commit()
            flash('Produit modifié avec succès!', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la sauvegarde: {str(e)}', 'error')
            
        return redirect(url_for('inventory.products'))
    
    return render_template('inventory/product_form.html',
                         title='Modifier un produit',
                         form=form,
                         product=product)

@bp.route('/products/<int:id>/delete', methods=['GET'])
@login_required
@permission_required
def confirm_delete_product(id):
    """Afficher la page de confirmation de suppression d'un produit"""
    product = Product.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    return render_template('inventory/confirm_delete_product.html', product=product)

@bp.route('/products/<int:id>/delete', methods=['POST'])
@login_required
@permission_required
def delete_product(id):
    """Supprimer un produit"""
    product = Product.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    try:
        # Supprimer d'abord la recette associée s'il y en a une
        from app.modules.inventory.models_recipe import Recipe
        recipe = Recipe.query.filter_by(product_id=id).first()
        if recipe:
            current_app.logger.info(f"Suppression de la recette associée au produit {product.name}")
            db.session.delete(recipe)
            db.session.flush()  # Flush pour s'assurer que la recette est bien supprimée avant de continuer
        
        # Supprimer les SaleItem associés s'il y en a
        from app.modules.pos.models_sale import SaleItem
        sale_items = SaleItem.query.filter_by(product_id=id).all()
        if sale_items:
            current_app.logger.info(f"Suppression de {len(sale_items)} éléments de vente associés au produit {product.name}")
            for item in sale_items:
                db.session.delete(item)
            db.session.flush()  # Flush pour s'assurer que tous les éléments sont supprimés
    
        # Supprimer les images associées si elles existent
        if product.image_path:
            try:
                # Supprimer l'image principale
                image_path = os.path.join('app', 'static', product.image_path)
                if os.path.exists(image_path):
                    os.remove(image_path)
                
                # Supprimer la miniature
                thumb_path = os.path.join('app', 'static', 'uploads', 'products', f"thumb_{os.path.basename(product.image_path)}")
                if os.path.exists(thumb_path):
                    os.remove(thumb_path)
            except Exception as e:
                current_app.logger.error(f"Erreur lors de la suppression des images du produit: {str(e)}")
        
        # Supprimer le produit
        db.session.delete(product)
        db.session.commit()
        flash('Produit supprimé avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        error_message = str(e)
        current_app.logger.error(f"Erreur lors de la suppression du produit: {error_message}", exc_info=True)
        flash(f'Erreur lors de la suppression: {error_message}', 'error')
    
    return redirect(url_for('inventory.products'))

@bp.route('/products/recipes')
@login_required
@permission_required
def products_recipes():
    """Liste des produits avec recettes"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category_id', type=int)
    sort_by = request.args.get('sort', 'name')
    sort_order = request.args.get('order', 'asc')
    active_only = request.args.get('active_only') == 'true'
    
    query = Product.query.filter_by(
        owner_id=current_user.id,
        has_recipe=True
    )
    
    # Filtrage par état actif
    if active_only:
        query = query.filter_by(is_active=True)
    
    # Filtrage par recherche
    if search:
        query = query.filter(Product.name.ilike(f'%{search}%'))
    
    # Filtrage par catégorie
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    # Tri des résultats
    if sort_by == 'name':
        if sort_order == 'asc':
            query = query.order_by(Product.name)
        else:
            query = query.order_by(Product.name.desc())
    elif sort_by == 'price':
        if sort_order == 'asc':
            query = query.order_by(Product.price)
        else:
            query = query.order_by(Product.price.desc())
    elif sort_by == 'category':
        # Jointure avec la table des catégories pour le tri
        if sort_order == 'asc':
            query = query.join(ProductCategory, Product.category_id == ProductCategory.id, isouter=True).order_by(ProductCategory.name)
        else:
            query = query.join(ProductCategory, Product.category_id == ProductCategory.id, isouter=True).order_by(ProductCategory.name.desc())
    
    # Pagination
    products_pagination = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Récupérer les catégories pour le filtre
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).order_by(ProductCategory.name).all()
    
    return render_template('inventory/products_recipes.html',
                         title='Produits avec recettes',
                         products=products_pagination.items,
                         pagination=products_pagination,
                         categories=categories,
                         search=search,
                         selected_category=category_id,
                         sort_by=sort_by,
                         sort_order=sort_order,
                         active_only=active_only,
                         per_page=per_page)

@bp.route('/products/<int:product_id>/recipe', methods=['GET', 'POST'])
@login_required
def recipe(product_id):
    product = Product.query.get_or_404(product_id)
    if not product.has_recipe:
        flash("Ce produit n'est pas basé sur une recette.", "error")
        return redirect(url_for('inventory.products'))
    
    recipe = Recipe.query.filter_by(product_id=product_id).first()
    if not recipe:
        recipe = Recipe(product_id=product_id)
        db.session.add(recipe)
        db.session.commit()
    
    form = RecipeForm(obj=recipe)
    ingredient_form = RecipeItemForm()
    
    # Populate ingredient choices
    ingredients = Ingredient.query.filter_by(owner_id=current_user.id).all()
    ingredient_form.ingredient_id.choices = [(i.id, f"{i.name} ({i.stock_quantity} {i.unit})") for i in ingredients]
    
    if form.validate_on_submit():
        form.populate_obj(recipe)
        db.session.commit()
        flash("Recette mise à jour avec succès!", "success")
        return redirect(url_for('inventory.recipe', product_id=product_id))
    
    return render_template('inventory/recipe.html',
                         product=product,
                         recipe=recipe,
                         form=form,
                         ingredient_form=ingredient_form)

@bp.route('/products/<int:product_id>/recipe/items', methods=['POST'])
@login_required
def add_recipe_item(product_id):
    recipe = Recipe.query.filter_by(product_id=product_id).first_or_404()
    form = RecipeItemForm()
    
    ingredients = Ingredient.query.filter_by(owner_id=current_user.id).all()
    form.ingredient_id.choices = [(i.id, i.name) for i in ingredients]
    
    if form.validate_on_submit():
        # Vérifier si l'ingrédient existe déjà dans la recette
        existing_item = RecipeItem.query.filter_by(
            recipe_id=recipe.id,
            ingredient_id=form.ingredient_id.data
        ).first()
        
        if existing_item:
            existing_item.quantity = form.quantity.data
            flash('Quantité mise à jour!', 'success')
        else:
            # Créer un nouvel élément de recette
            item = RecipeItem(
                recipe_id=recipe.id,
                ingredient_id=form.ingredient_id.data,
                quantity=form.quantity.data
            )
            db.session.add(item)
            flash('Ingrédient ajouté à la recette!', 'success')
        
        db.session.commit()
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f"{field}: {error}", "error")
    
    return redirect(url_for('inventory.recipe', product_id=product_id))

@bp.route('/products/<int:product_id>/recipe/items/<int:item_id>/remove', methods=['POST'])
@login_required
def remove_recipe_item(product_id, item_id):
    item = RecipeItem.query.get_or_404(item_id)
    if item.recipe.product_id != product_id:
        abort(400)
    
    db.session.delete(item)
    db.session.commit()
    flash('Ingrédient retiré de la recette!', 'success')
    
    return redirect(url_for('inventory.recipe', product_id=product_id))

@bp.route('/products/<int:product_id>/check-stock')
@login_required
def check_stock(product_id):
    product = Product.query.filter_by(id=product_id, owner_id=current_user.id).first_or_404()
    
    # Check if the product has a recipe
    if not product.has_recipe or not product.recipe:
        return jsonify({
            'stock': product.stock_quantity,
            'is_recipe_based': False
        })
    
    # Calculate maximum number of items that can be produced
    max_quantity = product.recipe.get_possible_quantity()
    return jsonify({
        'stock': max_quantity,
        'is_recipe_based': True
    })

@bp.route('/ingredients')
@login_required
@permission_required
def ingredients():
    """Liste des ingrédients"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category_id', type=int)
    sort_by = request.args.get('sort', 'name')
    sort_order = request.args.get('order', 'asc')
    
    query = Ingredient.query.filter_by(owner_id=current_user.id)
    
    # Filtre de recherche par nom
    if search:
        query = query.filter(Ingredient.name.ilike(f'%{search}%'))
    
    # Filtre par catégorie
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    # Tri des résultats
    if sort_by == 'name':
        if sort_order == 'asc':
            query = query.order_by(Ingredient.name)
        else:
            query = query.order_by(Ingredient.name.desc())
    elif sort_by == 'stock':
        if sort_order == 'asc':
            query = query.order_by(Ingredient.stock_quantity)
        else:
            query = query.order_by(Ingredient.stock_quantity.desc())
    elif sort_by == 'price':
        if sort_order == 'asc':
            query = query.order_by(Ingredient.price_per_unit)
        else:
            query = query.order_by(Ingredient.price_per_unit.desc())
    
    # Pagination
    ingredients_pagination = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Récupérer les catégories pour le filtre
    categories = IngredientCategory.query.filter_by(owner_id=current_user.id).order_by(IngredientCategory.name).all()
    
    return render_template('inventory/ingredients.html',
                         title='Ingrédients',
                         ingredients=ingredients_pagination.items,
                         pagination=ingredients_pagination,
                         categories=categories,
                         search=search,
                         selected_category=category_id,
                         sort_by=sort_by,
                         sort_order=sort_order,
                         per_page=per_page)

@bp.route('/ingredients/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_ingredient():
    """Ajouter un ingrédient"""
    form = IngredientForm()
    
    # Populate category choices
    categories = IngredientCategory.query.filter_by(owner_id=current_user.id).all()
    form.category_id.choices = [(0, 'Aucune')] + [(c.id, c.name) for c in categories]
    
    if form.validate_on_submit():
        ingredient = Ingredient(
            name=form.name.data,
            description=form.description.data,
            category_id=form.category_id.data if form.category_id.data != 0 else None,
            unit=form.unit.data,
            stock_quantity=form.stock_quantity.data,
            minimum_stock=form.minimum_stock.data,
            price_per_unit=form.price_per_unit.data,
            owner_id=current_user.id
        )
        
        if form.image.data:
            try:
                image_path = save_image(form.image.data, 'ingredients')
                if image_path:
                    ingredient.image_path = image_path
            except Exception as e:
                flash(f"Erreur lors de l'upload de l'image: {str(e)}", "error")
        
        db.session.add(ingredient)
        db.session.commit()
        flash('Ingrédient ajouté avec succès!', 'success')
        return redirect(url_for('inventory.ingredients'))
    
    return render_template('inventory/ingredient_form.html',
                         title='Ajouter un ingrédient',
                         form=form)

@bp.route('/ingredients/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_ingredient(id):
    """Modifier un ingrédient"""
    ingredient = Ingredient.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = IngredientForm(obj=ingredient)
    
    # Populate category choices
    categories = IngredientCategory.query.filter_by(owner_id=current_user.id).all()
    form.category_id.choices = [(0, 'Aucune')] + [(c.id, c.name) for c in categories]
    
    if form.validate_on_submit():
        ingredient.name = form.name.data
        ingredient.description = form.description.data
        ingredient.category_id = form.category_id.data if form.category_id.data != 0 else None
        ingredient.unit = form.unit.data
        ingredient.stock_quantity = form.stock_quantity.data
        ingredient.minimum_stock = form.minimum_stock.data
        ingredient.price_per_unit = form.price_per_unit.data
        
        if form.image.data:
            try:
                image_path = save_image(form.image.data, 'ingredients')
                if image_path:
                    # Supprimer l'ancienne image si elle existe
                    if ingredient.image_path:
                        old_image_path = os.path.join('app', 'static', ingredient.image_path)
                        if os.path.exists(old_image_path):
                            os.remove(old_image_path)
                    
                    ingredient.image_path = image_path
            except Exception as e:
                flash(f"Erreur lors de l'upload de l'image: {str(e)}", "error")
        
        db.session.commit()
        flash('Ingrédient modifié avec succès!', 'success')
        return redirect(url_for('inventory.ingredients'))
    
    return render_template('inventory/ingredient_form.html',
                         title='Modifier un ingrédient',
                         form=form,
                         ingredient=ingredient)

@bp.route('/ingredients/<int:id>/delete', methods=['POST'])
@login_required
@permission_required
def delete_ingredient(id):
    """Supprimer un ingrédient"""
    ingredient = Ingredient.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    # Check if ingredient is used in recipes
    recipes_using = RecipeItem.query.filter_by(ingredient_id=id).first()
    if recipes_using:
        flash("Impossible de supprimer cet ingrédient car il est utilisé dans des recettes.", "error")
        return redirect(url_for('inventory.ingredients'))
    
    try:
        db.session.delete(ingredient)
        db.session.commit()
        flash('Ingrédient supprimé avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la suppression: {str(e)}', 'error')
    
    return redirect(url_for('inventory.ingredients'))

@bp.route('/ingredients/<int:id>/stock', methods=['GET', 'POST'])
@login_required
@permission_required
def update_ingredient_stock(id):
    """Mettre à jour le stock d'un ingrédient"""
    ingredient = Ingredient.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = IngredientStockForm()
    
    if form.validate_on_submit():
        operation = form.operation.data
        quantity = form.quantity.data
        
        if operation == 'add':
            ingredient.stock_quantity += quantity
        elif operation == 'subtract':
            if ingredient.stock_quantity < quantity:
                flash(f"Impossible de retirer {quantity} {ingredient.unit}, seulement {ingredient.stock_quantity} disponibles.", "error")
                return redirect(url_for('inventory.ingredients'))
            ingredient.stock_quantity -= quantity
        elif operation == 'set':
            ingredient.stock_quantity = quantity
        
        db.session.commit()
        flash(f'Stock mis à jour avec succès! Nouveau stock: {ingredient.stock_quantity} {ingredient.unit}', 'success')
        return redirect(url_for('inventory.ingredients'))
    
    return render_template('inventory/ingredient_stock_form.html',
                        title='Mettre à jour le stock',
                        form=form,
                        ingredient=ingredient)

@bp.route('/ingredients/batch-stock', methods=['POST'])
@login_required
@permission_required
def update_batch_stock():
    """Mettre à jour le stock de plusieurs ingrédients en une fois"""
    data = request.json
    if not data or 'ingredients' not in data:
        return jsonify({'error': 'Format de données invalide'}), 400
    
    ingredients_data = data['ingredients']
    for item in ingredients_data:
        ingredient_id = item.get('id')
        if not ingredient_id:
            continue
        
        ingredient = Ingredient.query.filter_by(id=ingredient_id, owner_id=current_user.id).first()
        if not ingredient:
            continue
        
        quantity = item.get('quantity')
        operation = item.get('operation', 'set')
        
        if operation == 'add':
            ingredient.stock_quantity += quantity
        elif operation == 'subtract':
            ingredient.stock_quantity = max(0, ingredient.stock_quantity - quantity)
        else:  # set
            ingredient.stock_quantity = quantity
    
    db.session.commit()
    return jsonify({'success': True})

@bp.route('/ingredients/<int:id>/delete-image', methods=['POST'])
@login_required
@permission_required
def delete_ingredient_image(id):
    """Supprimer l'image d'un ingrédient"""
    ingredient = Ingredient.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    if not ingredient.image_path:
        flash("Cet ingrédient n'a pas d'image.", "warning")
        return redirect(url_for('inventory.edit_ingredient', id=id))
    
    try:
        # Supprimer le fichier
        image_path = os.path.join('app', 'static', ingredient.image_path)
        if os.path.exists(image_path):
            os.remove(image_path)
        
        # Mettre à jour la base de données
        ingredient.image_path = None
        db.session.commit()
        flash("Image supprimée avec succès!", "success")
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de la suppression de l'image: {str(e)}", "error")
    
    return redirect(url_for('inventory.edit_ingredient', id=id))

