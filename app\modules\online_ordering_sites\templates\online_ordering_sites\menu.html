{% extends "online_ordering_sites/base.html" %}

{% block title %}Menu - {{ site.site_name or site.owner.username }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h3 mb-2">
                    <i class="fas fa-utensils me-2 text-primary"></i>
                    Notre Menu
                </h1>
                <p class="text-muted mb-0">Découvrez nos délicieuses spécialités</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary active" id="grid-view">
                        <i class="fas fa-th"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="list-view">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters and Search -->
<section class="py-3 border-bottom">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-products" placeholder="Rechercher un produit...">
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex gap-2 flex-wrap justify-content-md-end mt-2 mt-md-0">
                    <button class="btn btn-sm btn-outline-secondary filter-btn active" data-category="all">
                        Tous
                    </button>
                    {% for category in menu_data.keys() %}
                        <button class="btn btn-sm btn-outline-secondary filter-btn" data-category="{{ category.id }}">
                            {{ category.name }}
                        </button>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Menu Content -->
<section class="py-4">
    <div class="container">
        {% if menu_data %}
            {% for category, products in menu_data.items() %}
                <div class="category-section mb-5" data-category="{{ category.id }}">
                    <div class="row align-items-center mb-4">
                        <div class="col">
                            <h2 class="h4 text-primary mb-0">
                                {% if category.image_path %}
                                    <img src="{{ url_for('static', filename=category.image_path) }}"
                                         alt="{{ category.name }}" class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%;">
                                {% else %}
                                    <i class="fas fa-utensils me-2"></i>
                                {% endif %}
                                {{ category.name }}
                            </h2>
                            {% if category.description %}
                                <p class="text-muted small mb-0">{{ category.description }}</p>
                            {% endif %}
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-secondary">{{ products|length }} produit{{ 's' if products|length > 1 else '' }}</span>
                        </div>
                    </div>
                    
                    <div class="row products-grid" id="products-{{ category.id }}">
                        {% for product in products %}
                            <div class="col-xxl-2 col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-4 product-item"
                                 data-name="{{ product.name.lower() }}" 
                                 data-category="{{ category.id }}"
                                 data-price="{{ product.price }}">
                                <div class="card product-card h-100 border-0 shadow-sm">
                                    {% if product.image_path %}
                                        <img src="{{ url_for('static', filename=product.image_path) }}"
                                             class="card-img-top" alt="{{ product.name }}"
                                             style="height: 200px; object-fit: cover;">
                                    {% else %}
                                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                             style="height: 200px;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="card-body d-flex flex-column">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h5 class="card-title mb-0">{{ product.name }}</h5>
                                            <button class="btn btn-sm btn-outline-danger favorite-btn {% if product.id in customer_favorites %}is-favorite{% endif %}"
                                                    data-product-id="{{ product.id }}"
                                                    data-is-favorite="{% if product.id in customer_favorites %}true{% else %}false{% endif %}"
                                                    title="{% if product.id in customer_favorites %}Retirer des favoris{% else %}Ajouter aux favoris{% endif %}">
                                                <i class="{% if product.id in customer_favorites %}fas{% else %}far{% endif %} fa-heart"></i>
                                            </button>
                                        </div>
                                        {% if product.description %}
                                            <p class="card-text text-muted small flex-grow-1">
                                                {{ product.description[:100] }}{% if product.description|length > 100 %}...{% endif %}
                                            </p>
                                        {% endif %}
                                        
                                        <div class="mt-auto">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <span class="h5 text-primary mb-0">{{ "%.2f"|format(product.price) }}€</span>
                                                {% if not product.is_available %}
                                                    <span class="badge bg-danger">Indisponible</span>
                                                {% elif product.stock_quantity is defined and product.stock_quantity <= 5 %}
                                                    <span class="badge bg-warning">Stock limité</span>
                                                {% endif %}
                                            </div>
                                            
                                            {% if product.is_available %}
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="input-group input-group-sm" style="width: 120px;">
                                                        <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="decrease">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                        <input type="number" class="form-control text-center quantity-input" 
                                                               value="1" min="1" max="10" data-product-id="{{ product.id }}">
                                                        <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="increase">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                    <button class="btn btn-primary flex-grow-1 add-to-cart-btn" 
                                                            data-product-id="{{ product.id }}"
                                                            data-product-name="{{ product.name }}"
                                                            data-product-price="{{ product.price }}">
                                                        <i class="fas fa-cart-plus me-1"></i>
                                                        Ajouter
                                                    </button>
                                                </div>
                                            {% else %}
                                                <button class="btn btn-secondary w-100" disabled>
                                                    <i class="fas fa-times me-1"></i>
                                                    Indisponible
                                                </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-5x text-muted mb-4"></i>
                <h3 class="text-muted">Aucun produit disponible</h3>
                <p class="text-muted">Le menu sera bientôt disponible.</p>
            </div>
        {% endif %}
    </div>
</section>

<!-- Sticky Cart Summary (Mobile) -->
<div class="sticky-cart d-md-none">
    <button class="btn btn-primary btn-lg rounded-pill shadow" id="mobile-cart-btn" data-bs-toggle="modal" data-bs-target="#cartModal">
        <i class="fas fa-shopping-cart me-2"></i>
        <span id="mobile-cart-count">0</span> article(s)
        <span class="ms-2" id="mobile-cart-total">0.00€</span>
    </button>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .product-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
    
    .filter-btn.active {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }
    
    .quantity-btn {
        width: 35px;
        padding: 0.25rem 0;
    }
    
    .quantity-input {
        border-left: 0;
        border-right: 0;
    }

    .favorite-btn {
        border: none !important;
        padding: 0.25rem 0.5rem;
        transition: all 0.2s ease;
    }

    .favorite-btn:hover {
        background-color: #dc3545;
        color: white;
    }

    .favorite-btn.is-favorite {
        background-color: #dc3545;
        color: white;
    }

    .favorite-btn.is-favorite i {
        color: white;
    }
    
    .sticky-cart {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
    }
    
    .products-grid.list-view .col-xl-3,
    .products-grid.list-view .col-lg-4,
    .products-grid.list-view .col-md-6,
    .products-grid.list-view .col-sm-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .products-grid.list-view .product-card {
        flex-direction: row;
    }
    
    .products-grid.list-view .card-img-top {
        width: 150px;
        height: 120px;
        flex-shrink: 0;
    }
    
    /* Améliorations pour les catégories */
    .category-section h2 img {
        border: 2px solid #fff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* Améliorations pour les cartes produits */
    .product-card .card-body {
        padding: 1.25rem;
    }

    .product-card .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
    }

    /* Responsive amélioré */
    @media (max-width: 768px) {
        .products-grid.list-view .product-card {
            flex-direction: column;
        }

        .products-grid.list-view .card-img-top {
            width: 100%;
            height: 200px;
        }

        /* Amélioration mobile pour la grille normale */
        .product-card .card-body {
            padding: 1rem;
        }

        .product-card .card-title {
            font-size: 1rem;
        }
    }

    @media (max-width: 576px) {
        /* Sur très petits écrans, forcer 2 colonnes pour une meilleure utilisation de l'espace */
        .col-sm-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .product-card .card-body {
            padding: 0.75rem;
        }

        .product-card .card-title {
            font-size: 0.9rem;
        }

        .product-card img,
        .product-card .card-img-top {
            height: 150px !important;
        }
    }

    /* Améliorations générales pour l'affichage en grille */
    .products-grid {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }

    .products-grid > [class*="col-"] {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Amélioration de l'espacement des cartes */
    .product-card {
        height: 100%;
        border-radius: 12px;
        overflow: hidden;
    }

    .product-card .card-img-top {
        border-radius: 0;
    }

    /* Amélioration des boutons */
    .add-to-cart-btn {
        border-radius: 8px;
        font-weight: 500;
    }

    .quantity-btn {
        border-radius: 6px;
    }

    /* Amélioration de l'affichage des prix */
    .h5.text-primary {
        font-weight: 700;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View toggle
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');
    const productsGrids = document.querySelectorAll('.products-grid');
    
    gridViewBtn.addEventListener('click', function() {
        this.classList.add('active');
        listViewBtn.classList.remove('active');
        productsGrids.forEach(grid => grid.classList.remove('list-view'));
    });
    
    listViewBtn.addEventListener('click', function() {
        this.classList.add('active');
        gridViewBtn.classList.remove('active');
        productsGrids.forEach(grid => grid.classList.add('list-view'));
    });
    
    // Category filter
    const filterBtns = document.querySelectorAll('.filter-btn');
    const categorySections = document.querySelectorAll('.category-section');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const category = this.getAttribute('data-category');
            
            categorySections.forEach(section => {
                if (category === 'all' || section.getAttribute('data-category') === category) {
                    section.style.display = 'block';
                } else {
                    section.style.display = 'none';
                }
            });
        });
    });
    
    // Search functionality
    const searchInput = document.getElementById('search-products');
    const productItems = document.querySelectorAll('.product-item');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        productItems.forEach(item => {
            const productName = item.getAttribute('data-name');
            if (productName.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // Quantity controls
    document.addEventListener('click', function(e) {
        if (e.target.closest('.quantity-btn')) {
            const btn = e.target.closest('.quantity-btn');
            const action = btn.getAttribute('data-action');
            const input = btn.parentElement.querySelector('.quantity-input');
            let value = parseInt(input.value);
            
            if (action === 'increase' && value < 10) {
                input.value = value + 1;
            } else if (action === 'decrease' && value > 1) {
                input.value = value - 1;
            }
        }
    });
    
    // Add to cart functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.add-to-cart-btn')) {
            const btn = e.target.closest('.add-to-cart-btn');
            const productId = btn.getAttribute('data-product-id');
            const productName = btn.getAttribute('data-product-name');
            const productPrice = parseFloat(btn.getAttribute('data-product-price'));
            const quantityInput = btn.parentElement.querySelector('.quantity-input');
            const quantity = parseInt(quantityInput.value);

            addToCart(productId, productName, productPrice, quantity);

            // Reset quantity to 1
            quantityInput.value = 1;

            // Visual feedback
            btn.innerHTML = '<i class="fas fa-check me-1"></i>Ajouté !';
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-success');

            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-cart-plus me-1"></i>Ajouter';
                btn.classList.remove('btn-success');
                btn.classList.add('btn-primary');
            }, 1500);
        }
    });

    // Favorite toggle functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.favorite-btn')) {
            const btn = e.target.closest('.favorite-btn');
            const productId = btn.getAttribute('data-product-id');

            toggleFavorite(productId, btn);
        }
    });
    
    // Update mobile cart display
    function updateMobileCart() {
        const cartCount = cart.reduce((sum, item) => sum + item.quantity, 0);
        const cartTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        document.getElementById('mobile-cart-count').textContent = cartCount;
        document.getElementById('mobile-cart-total').textContent = cartTotal.toFixed(2) + '€';
        
        const mobileCartBtn = document.getElementById('mobile-cart-btn');
        if (cartCount > 0) {
            mobileCartBtn.style.display = 'block';
        } else {
            mobileCartBtn.style.display = 'none';
        }
    }
    
    // Override the updateCartDisplay function from base template
    const originalUpdateCartDisplay = window.updateCartDisplay;
    window.updateCartDisplay = function() {
        originalUpdateCartDisplay();
        updateMobileCart();
    };
    
    // Initial mobile cart update
    updateMobileCart();

    // Load favorite status for all products
    loadFavoriteStatus();
});

function toggleFavorite(productId, btn) {
    fetch('/api/toggle_favorite', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => {
        // Vérifier si la réponse est du JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Réponse non-JSON reçue');
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            const icon = btn.querySelector('i');

            if (data.is_favorite) {
                btn.classList.add('is-favorite');
                btn.setAttribute('data-is-favorite', 'true');
                btn.setAttribute('title', 'Retirer des favoris');
                icon.className = 'fas fa-heart';
            } else {
                btn.classList.remove('is-favorite');
                btn.setAttribute('data-is-favorite', 'false');
                btn.setAttribute('title', 'Ajouter aux favoris');
                icon.className = 'far fa-heart';
            }

            // Afficher un message de succès
            showToast(data.message, 'success');
        } else if (data.status === 'error') {
            // Gérer le cas où l'utilisateur n'est pas connecté
            if (data.redirect) {
                showToast('Veuillez vous connecter pour gérer vos favoris', 'warning');
                // Optionnel: rediriger vers la page de connexion après un délai
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            } else {
                showToast(data.message, 'error');
            }
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showToast('Veuillez vous connecter pour gérer vos favoris', 'warning');
    });
}

function loadFavoriteStatus() {
    // Cette fonction sera appelée pour charger l'état des favoris au chargement de la page
    // Pour l'instant, on peut la laisser vide car on n'a pas encore d'API pour récupérer tous les favoris
}

function showToast(message, type) {
    // Créer un toast simple
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // Supprimer automatiquement après 3 secondes
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
