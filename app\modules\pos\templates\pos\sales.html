{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-shopping-cart"></i> {{ title }}
        </h2>
        <a href="{{ url_for('pos.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour au POS
        </a>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET">
                <!-- Première ligne de filtres -->
                <div class="row g-3 mb-3">
                    <div class="col-md-2">
                        <label class="form-label">Statut</label>
                        <select name="status" class="form-select">
                            <option value="">Tous les statuts</option>
                            {% for status_value, status_label in statuses %}
                            <option value="{{ status_value }}" {% if status == status_value %}selected{% endif %}>
                                {% if status_value == 'pending' %}En attente
                                {% elif status_value == 'kitchen_pending' %}Cuisine en attente
                                {% elif status_value == 'kitchen_ready' %}Cuisine prêt
                                {% elif status_value == 'delivered' %}Servi
                                {% elif status_value == 'paid' %}Payé
                                {% elif status_value == 'cancelled' %}Annulé
                                {% elif status_value == 'completed' %}Terminé
                                {% elif status_value == 'voided' %}Annulé (void)
                                {% elif status_value == 'ready' %}Prêt
                                {% else %}{{ status_value|title }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Utilisateur</label>
                        <select name="user_id" class="form-select">
                            <option value="">Tous</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if user_id and user.id == user_id|int %}selected{% endif %}>
                                {{ user.username }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Salle</label>
                        <select name="room_id" class="form-select">
                            <option value="">Toutes</option>
                            {% for room in rooms %}
                            <option value="{{ room.id }}" {% if room_id and room.id == room_id|int %}selected{% endif %}>
                                {{ room.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Type de service</label>
                        <select name="service_type" class="form-select">
                            <option value="">Tous les types</option>
                            {% for service_value, service_label in service_types %}
                            <option value="{{ service_value }}" {% if service_type == service_value %}selected{% endif %}>
                                {{ service_label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Source</label>
                        <select name="order_source" class="form-select">
                            <option value="all" {% if order_source == 'all' %}selected{% endif %}>Toutes</option>
                            <option value="pos" {% if order_source == 'pos' %}selected{% endif %}>POS</option>
                            <option value="online" {% if order_source == 'online' %}selected{% endif %}>En ligne</option>
                        </select>
                    </div>
                </div>

                <!-- Deuxième ligne de filtres -->
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Date début</label>
                        <input type="date" name="date_from" class="form-control" value="{{ date_from.strftime('%Y-%m-%d') if date_from }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date fin</label>
                        <input type="date" name="date_to" class="form-control" value="{{ date_to.strftime('%Y-%m-%d') if date_to }}">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Filtrer
                        </button>
                        <a href="{{ url_for('pos.sales') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des ventes -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Source</th>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Utilisateur</th>
                            <th>Client</th>
                            <th>Table/Salle</th>
                            <th>Type de service</th>
                            <th>Total</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Ventes POS -->
                        {% for sale in sales.items %}
                        <tr>
                            <td><span class="badge bg-primary">POS</span></td>
                            <td>{{ sale.reference }}</td>
                            <td>{{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td>
                                {% if sale.user %}
                                    <span class="badge bg-info">{{ sale.user.username }}</span>
                                {% else %}
                                    <span class="text-muted">Non défini</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.customer %}
                                {{ sale.customer.first_name }} {{ sale.customer.last_name }}
                                {% else %}
                                Client anonyme
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.table %}
                                    <strong>Table {{ sale.table.number }}</strong>
                                    {% if sale.table.room %}
                                        <br><small class="text-muted">{{ sale.table.room.name }}</small>
                                    {% elif sale.table.location %}
                                        <br><small class="text-muted">({{ sale.table.location }})</small>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">Non attribuée</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.service_type %}
                                    {% if sale.service_type == 'dine_in' %}
                                        <span class="badge bg-primary">Sur place</span>
                                    {% elif sale.service_type == 'takeaway' %}
                                        <span class="badge bg-warning">À emporter</span>
                                    {% elif sale.service_type == 'delivery' %}
                                        <span class="badge bg-info">Livraison</span>
                                    {% elif sale.service_type == 'drive_thru' %}
                                        <span class="badge bg-success">Service au volant</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ sale.service_type }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">Non défini</span>
                                {% endif %}
                            </td>
                            <td>{{ "%.2f"|format(sale.total) }} €</td>
                            <td>
                                {% if sale.status.value == 'pending' %}
                                <span class="badge bg-warning">En attente</span>
                                {% elif sale.status.value == 'kitchen_pending' %}
                                <span class="badge bg-primary">Cuisine en attente</span>
                                {% elif sale.status.value == 'kitchen_ready' %}
                                <span class="badge bg-info">Cuisine prêt</span>
                                {% elif sale.status.value == 'delivered' %}
                                <span class="badge bg-secondary">Servi</span>
                                {% elif sale.status.value == 'paid' %}
                                <span class="badge bg-success">Payé</span>
                                {% elif sale.status.value == 'cancelled' %}
                                <span class="badge bg-danger">Annulé</span>
                                {% elif sale.status.value == 'completed' %}
                                <span class="badge bg-dark">Terminé</span>
                                {% else %}
                                <span class="badge bg-light text-dark">{{ sale.status.value }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('pos.sale_details', id=sale.id) }}"
                                   class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}

                        <!-- Commandes en ligne -->
                        {% for order in online_orders %}
                        <tr>
                            <td><span class="badge bg-success">En ligne</span></td>
                            <td>#{{ order.order_number }}</td>
                            <td>{{ order.ordered_at.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td><span class="badge bg-success">Commande en ligne</span></td>
                            <td>{{ order.customer.first_name }} {{ order.customer.last_name }}</td>
                            <td>
                                {% if order.order_type.value == 'delivery' %}
                                    <span class="text-info">Livraison</span>
                                    {% if order.delivery_address %}
                                        <br><small class="text-muted">{{ order.delivery_address[:30] }}...</small>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">{{ order.order_type.value.replace('_', ' ').title() }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if order.order_type.value == 'delivery' %}
                                    <span class="badge bg-info">Livraison</span>
                                {% elif order.order_type.value == 'pickup' %}
                                    <span class="badge bg-warning">À emporter</span>
                                {% elif order.order_type.value == 'dine_in' %}
                                    <span class="badge bg-primary">Sur place</span>
                                {% elif order.order_type.value == 'drive_through' %}
                                    <span class="badge bg-success">Drive</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ order.order_type.value }}</span>
                                {% endif %}
                            </td>
                            <td>{{ "%.2f"|format(order.total_amount) }} €</td>
                            <td>
                                {% if order.status.value == 'pending' %}
                                <span class="badge bg-warning">En attente</span>
                                {% elif order.status.value == 'confirmed' %}
                                <span class="badge bg-info">Confirmée</span>
                                {% elif order.status.value == 'preparing' %}
                                <span class="badge bg-primary">En préparation</span>
                                {% elif order.status.value == 'ready' %}
                                <span class="badge bg-success">Prête</span>
                                {% elif order.status.value == 'out_for_delivery' %}
                                <span class="badge bg-info">En livraison</span>
                                {% elif order.status.value == 'delivered' %}
                                <span class="badge bg-dark">Livrée</span>
                                {% elif order.status.value == 'cancelled' %}
                                <span class="badge bg-danger">Annulée</span>
                                {% else %}
                                <span class="badge bg-light text-dark">{{ order.status.value }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('online_ordering_sites.admin_orders') }}"
                                       class="btn btn-info" title="Voir dans gestion commandes">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if order.payment_method.value == 'cash_on_delivery' and order.status.value in ['ready', 'out_for_delivery'] %}
                                        <button class="btn btn-success" onclick="processOnlineOrderPayment({{ order.id }})" title="Traiter le paiement">
                                            <i class="fas fa-credit-card"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}

                        {% if not sales.items and not online_orders %}
                        <tr>
                            <td colspan="10" class="text-center">Aucune commande trouvée</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if sales.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% for page in range(1, sales.pages + 1) %}
                    <li class="page-item {% if page == sales.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('pos.sales',
                            page=page,
                            status=status,
                            user_id=user_id,
                            room_id=room_id,
                            service_type=service_type,
                            date_from=date_from.strftime('%Y-%m-%d') if date_from,
                            date_to=date_to.strftime('%Y-%m-%d') if date_to) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal pour traiter le paiement des commandes en ligne -->
<div class="modal fade" id="onlinePaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Traiter le paiement - Commande en ligne</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="order-details"></div>
                <hr>
                <form id="payment-form">
                    <div class="mb-3">
                        <label for="payment-method" class="form-label">Méthode de paiement</label>
                        <select class="form-select" id="payment-method" required>
                            <option value="cash">Espèces</option>
                            <option value="card">Carte bancaire</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="amount-received" class="form-label">Montant reçu</label>
                        <input type="number" class="form-control" id="amount-received" step="0.01" required>
                    </div>
                    <div id="change-amount" class="alert alert-info" style="display: none;">
                        <strong>Monnaie à rendre : <span id="change-value">0.00</span>€</strong>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" id="confirm-payment">Confirmer le paiement</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentOrderId = null;
let orderTotal = 0;

function processOnlineOrderPayment(orderId) {
    currentOrderId = orderId;

    // Récupérer les détails de la commande
    fetch(`/admin/orders/${orderId}/details`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            document.getElementById('order-details').innerHTML = data.html;
            orderTotal = data.total;
            document.getElementById('amount-received').value = orderTotal.toFixed(2);

            const modal = new bootstrap.Modal(document.getElementById('onlinePaymentModal'));
            modal.show();
        } else {
            alert('Erreur lors du chargement des détails de la commande');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur de connexion');
    });
}

// Calculer la monnaie à rendre
document.getElementById('amount-received').addEventListener('input', function() {
    const amountReceived = parseFloat(this.value) || 0;
    const change = amountReceived - orderTotal;

    const changeDiv = document.getElementById('change-amount');
    const changeValue = document.getElementById('change-value');

    if (change >= 0) {
        changeValue.textContent = change.toFixed(2);
        changeDiv.style.display = 'block';
        changeDiv.className = 'alert alert-info';
    } else {
        changeDiv.style.display = 'none';
    }
});

// Confirmer le paiement
document.getElementById('confirm-payment').addEventListener('click', function() {
    const paymentMethod = document.getElementById('payment-method').value;
    const amountReceived = parseFloat(document.getElementById('amount-received').value);

    if (amountReceived < orderTotal) {
        alert('Le montant reçu est insuffisant');
        return;
    }

    // Traiter le paiement
    fetch(`/pos/process_online_order_payment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            order_id: currentOrderId,
            payment_method: paymentMethod,
            amount_received: amountReceived
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Paiement traité avec succès');
            location.reload();
        } else {
            alert('Erreur: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur de connexion');
    });
});
</script>
{% endblock %}