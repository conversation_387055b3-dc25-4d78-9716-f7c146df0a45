from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.extensions import db
from app.modules.inventory.models_supplier import Supplier
from app.utils.decorators import inventory_access_required as permission_required
from . import bp
from .forms import SupplierForm

@bp.route('/suppliers')
@login_required
@permission_required
def suppliers():
    """Liste des fournisseurs"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    
    query = Supplier.query.filter_by(owner_id=current_user.id)
    
    if search:
        query = query.filter(Supplier.name.ilike(f'%{search}%'))
    
    suppliers = query.order_by(Supplier.name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('inventory/suppliers/list.html',
                         title='Fournisseurs',
                         suppliers=suppliers,
                         search=search,
                         per_page=per_page)

@bp.route('/suppliers/add', methods=['GET', 'POST'])
@login_required
@permission_required
def add_supplier():
    """Ajouter un fournisseur"""
    form = SupplierForm()
    
    if form.validate_on_submit():
        supplier = Supplier(
            name=form.name.data,
            contact_name=form.contact_name.data,
            email=form.contact_email.data,
            phone=form.contact_phone.data,
            address=form.address.data,
            notes=form.notes.data,
            owner_id=current_user.id
        )
        
        db.session.add(supplier)
        db.session.commit()
        flash('Fournisseur ajouté avec succès!', 'success')
        return redirect(url_for('inventory.suppliers'))
    
    return render_template('inventory/suppliers/form.html',
                         title='Ajouter un fournisseur',
                         form=form)

@bp.route('/suppliers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_supplier(id):
    """Modifier un fournisseur"""
    supplier = Supplier.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    # Initialiser le formulaire avec les valeurs du fournisseur
    if request.method == 'GET':
        form = SupplierForm()
        form.name.data = supplier.name
        form.contact_name.data = supplier.contact_name
        form.contact_email.data = supplier.email
        form.contact_phone.data = supplier.phone
        form.address.data = supplier.address
        form.notes.data = supplier.notes
    else:
        form = SupplierForm()
    
    if form.validate_on_submit():
        supplier.name = form.name.data
        supplier.contact_name = form.contact_name.data
        supplier.email = form.contact_email.data
        supplier.phone = form.contact_phone.data
        supplier.address = form.address.data
        supplier.notes = form.notes.data
        
        db.session.commit()
        flash('Fournisseur modifié avec succès!', 'success')
        return redirect(url_for('inventory.suppliers'))
    
    return render_template('inventory/suppliers/form.html',
                         title='Modifier un fournisseur',
                         form=form,
                         supplier=supplier)

@bp.route('/suppliers/<int:id>/delete', methods=['POST'])
@login_required
@permission_required
def delete_supplier(id):
    """Supprimer un fournisseur"""
    supplier = Supplier.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    # Vérifier si des ingrédients sont associés à ce fournisseur
    ingredients_count = supplier.ingredients.count()
    if ingredients_count > 0:
        flash(f'Impossible de supprimer ce fournisseur, {ingredients_count} ingrédient(s) y sont associés.', 'error')
        return redirect(url_for('inventory.suppliers'))
    
    db.session.delete(supplier)
    db.session.commit()
    flash('Fournisseur supprimé avec succès!', 'success')
    return redirect(url_for('inventory.suppliers')) 