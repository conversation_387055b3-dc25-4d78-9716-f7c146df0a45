{% if movements %}
<div class="table-responsive">
    <table class="table">
        <thead>
            <tr>
                <th>Date</th>
                <th>Type</th>
                <th>Quantité</th>
                <th>Note</th>
                <th>Utilisateur</th>
            </tr>
        </thead>
        <tbody>
            {% for movement in movements %}
            <tr>
                <td>{{ movement.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                <td>
                    {% if movement.type == 'IN' %}
                    <span class="badge bg-success">Entrée</span>
                    {% else %}
                    <span class="badge bg-danger">Sortie</span>
                    {% endif %}
                </td>
                <td>{{ movement.quantity }}</td>
                <td>{{ movement.note or '-' }}</td>
                <td>{{ movement.user.username if movement.user else '-' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<p class="text-muted">Aucun mouvement trouvé pour cet article.</p>
{% endif %} 