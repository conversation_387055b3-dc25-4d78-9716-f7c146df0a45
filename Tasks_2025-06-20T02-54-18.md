[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:Fix owner_id missing in core models DESCRIPTION:Add owner_id field to ProductVariant, Recipe models, and Table/Reservation models to ensure proper multi-tenant support
-[ ] NAME:Implement budget functionality in expenses module DESCRIPTION:Create budget function in expenses/views.py, corresponding template.html, and add routing in urls.py
-[ ] NAME:Complete Product Variants implementation DESCRIPTION:Implement Product Variants with owner_id in inventory/models_product.py, create template.html and forms.py
-[/] NAME:Fix inventory quantity reduction bug DESCRIPTION:Fix issue where product quantities don't reduce when marking orders as served and processing payments, including products with recipes
-[ ] NAME:Complete missing settings routes and templates DESCRIPTION:Implement missing routes (system backup, alerts) in settings/views.py and create corresponding templates for general.html and sale.html
-[ ] NAME:Fix supplier categories route and template DESCRIPTION:Implement missing supplier_categories route and update corresponding HTML template
-[ ] NAME:Create unified reports dashboard DESCRIPTION:Create reports index.html with dropdown for report selection and customization form with radio buttons
-[ ] NAME:Implement receipt functionality DESCRIPTION:Create missing function for reports/receipt.html in reports/views.py
-[ ] NAME:Add sales reports by rooms and tables DESCRIPTION:Create sales reports with filters for rooms/tables showing revenue, customer count, and article statistics
-[ ] NAME:Code optimization and cleanup DESCRIPTION:Review all modules to identify unused functions/files and optimize code structure