from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.modules.tables.models_table import Table, TableStatus, TableReservation, Room
from app import db
from app.modules.tables.forms_table import TableForm, TableReservationForm
from app.modules.tables.utils import save_table_image, delete_table_image
from datetime import datetime, timedelta
from app.utils.decorators import permission_required

from . import bp

@bp.route('/')
@login_required
@permission_required('can_manage_tables')
def index():
    owner_id = current_user.get_owner_id
    room_id = request.args.get('room_id', type=int)
    
    # Base query
    query = Table.query.options(
        db.joinedload(Table.room)
    ).filter_by(owner_id=owner_id)
    
    # Appliquer le filtre par salle si spécifié
    if room_id:
        query = query.filter_by(room_id=room_id)
    
    # Trier les tables par numéro
    tables = query.order_by(Table.number).all()
    
    # Récupérer toutes les salles pour le filtre
    rooms = Room.query.filter_by(owner_id=owner_id, is_active=True).all()
    
    return render_template('tables/index.html', tables=tables, rooms=rooms, selected_room_id=room_id)

@bp.route('/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_tables')
def new():
    form = TableForm()
    if form.validate_on_submit():
        # Gérer l'upload d'image si nécessaire
        image_filename = None
        use_image = form.display_type.data == 'image'

        if use_image and form.table_image.data:
            image_filename = save_table_image(form.table_image.data)
            if not image_filename:
                flash('Erreur lors de l\'upload de l\'image. Utilisation de la forme géométrique.', 'warning')
                use_image = False

        table = Table(
            owner_id=current_user.id,
            room_id=form.room_id.data,
            number=form.number.data,
            capacity=form.capacity.data,
            location=form.location.data,
            table_shape=form.table_shape.data,
            table_size=form.table_size.data,
            table_color=form.table_color.data,
            table_image=image_filename,
            use_image=use_image,
            position_x=int(form.position_x.data) if form.position_x.data else 100,
            position_y=int(form.position_y.data) if form.position_y.data else 100
        )
        db.session.add(table)
        db.session.commit()
        flash('Table ajoutée avec succès!', 'success')
        return redirect(url_for('tables.index'))
    return render_template('tables/form.html', form=form, title="Nouvelle Table")

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_tables')
def edit(id):
    table = Table.query.get_or_404(id)
    form = TableForm(obj=table)

    # Pré-remplir le type d'affichage
    if not form.display_type.data:
        form.display_type.data = 'image' if table.use_image else 'shape'

    if form.validate_on_submit():
        # Gérer l'upload d'image si nécessaire
        use_image = form.display_type.data == 'image'
        old_image = table.table_image

        if use_image and form.table_image.data:
            # Nouvelle image uploadée
            new_image = save_table_image(form.table_image.data, table.id)
            if new_image:
                # Supprimer l'ancienne image si elle existe
                if old_image:
                    delete_table_image(old_image)
                table.table_image = new_image
            else:
                flash('Erreur lors de l\'upload de l\'image. Image précédente conservée.', 'warning')
        elif not use_image and old_image:
            # Passage de image à forme géométrique
            delete_table_image(old_image)
            table.table_image = None

        table.room_id = form.room_id.data
        table.number = form.number.data
        table.capacity = form.capacity.data
        table.location = form.location.data
        table.table_shape = form.table_shape.data
        table.table_size = form.table_size.data
        table.table_color = form.table_color.data
        table.use_image = use_image
        # Ne pas modifier la position lors de l'édition via formulaire
        db.session.commit()
        flash('Table mise à jour avec succès!', 'success')
        return redirect(url_for('tables.index'))
    return render_template('tables/form.html', form=form, table=table, title="Modifier Table")

@bp.route('/<int:id>')
@login_required
@permission_required('can_manage_tables')
def show(id):
    table = Table.query.get_or_404(id)
    today = datetime.now().date()
    return render_template('tables/show.html', table=table, today=today)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def delete(id):
    table = Table.query.get_or_404(id)
    if table.current_order_id:
        flash('Impossible de supprimer une table avec une commande en cours!', 'error')
        return redirect(url_for('tables.index'))

    # Supprimer l'image associée si elle existe
    if table.table_image:
        delete_table_image(table.table_image)

    db.session.delete(table)
    db.session.commit()
    flash('Table supprimée avec succès!', 'success')
    return redirect(url_for('tables.index'))

@bp.route('/<int:id>/status', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def update_status(id):
    table = Table.query.get_or_404(id)
    data = request.get_json()
    new_status = data.get('status')
    
    if new_status not in [status for status in dir(TableStatus) if not status.startswith('_')]:
        return jsonify({'success': False, 'message': 'Statut invalide'}), 400
        
    table.status = new_status
    db.session.commit()
    return jsonify({'success': True})

@bp.route('/<int:id>/reserve', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_tables')
def reserve(id):
    table = Table.query.get_or_404(id)
    form = TableReservationForm()
    
    if form.validate_on_submit():
        # Vérifier si la table est déjà réservée pour cette période
        existing_reservation = TableReservation.query.filter(
            TableReservation.table_id == table.id,
            TableReservation.reservation_date <= form.reservation_date.data + timedelta(minutes=form.duration_minutes.data),
            TableReservation.reservation_date + timedelta(minutes=TableReservation.duration_minutes) >= form.reservation_date.data
        ).first()
        
        if existing_reservation:
            flash('Cette table est déjà réservée pour cette période!', 'error')
        else:
            reservation = TableReservation(
                table_id=table.id,
                customer_name=form.customer_name.data,
                customer_phone=form.customer_phone.data,
                number_of_guests=form.number_of_guests.data,
                reservation_date=form.reservation_date.data,
                duration_minutes=form.duration_minutes.data,
                notes=form.notes.data
            )
            db.session.add(reservation)
            db.session.commit()
            flash('Réservation créée avec succès!', 'success')
            return redirect(url_for('tables.show', id=table.id))
            
    return render_template('tables/reserve.html', form=form, table=table)

@bp.route('/reservations')
@login_required
@permission_required('can_manage_tables')
def reservations():
    date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
    reservations = TableReservation.query.filter(
        TableReservation.reservation_date >= datetime.strptime(date, '%Y-%m-%d'),
        TableReservation.reservation_date < datetime.strptime(date, '%Y-%m-%d') + timedelta(days=1)
    ).order_by(TableReservation.reservation_date).all()
    return render_template('tables/reservations.html', reservations=reservations, selected_date=date)

@bp.route('/reservations/<int:id>/cancel', methods=['POST'])
@login_required
@permission_required('can_manage_tables')
def cancel_reservation(id):
    reservation = TableReservation.query.get_or_404(id)
    db.session.delete(reservation)
    db.session.commit()
    flash('Réservation annulée avec succès!', 'success')
    return redirect(url_for('tables.reservations')) 