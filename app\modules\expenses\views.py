from flask import render_template, flash, redirect, url_for, request, jsonify, send_file
from flask_login import login_required, current_user
from app.modules.expenses import bp
from app.utils.decorators import permission_required
from app import db
from app.modules.expenses.models_expense import ExpenseCategory, Expense, Budget
from app.modules.expenses.forms_expense import ExpenseCategoryForm, ExpenseForm, BudgetForm, ExpenseFilterForm
from datetime import datetime, timedelta
import os
import csv
import io
from openpyxl import Workbook
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from sqlalchemy import func

@bp.route('/')
@login_required
@permission_required('can_manage_expenses')
def index():
    form = ExpenseFilterForm()
    form.category_id.choices = [('', 'Toutes')] + [(str(c.id), c.name) for c in ExpenseCategory.query.filter_by(owner_id=current_user.id).order_by(ExpenseCategory.name)]
    
    # Paramètres de pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10  # Nombre d'éléments par page
    
    # Paramètres de tri
    sort_by = request.args.get('sort_by', 'date')
    sort_order = request.args.get('sort_order', 'desc')
    
    # Construction de la requête de base
    query = Expense.query.filter_by(owner_id=current_user.id)
    
    # Appliquer les filtres
    category_id = request.args.get('category_id')
    payment_method = request.args.get('payment_method')
    min_amount = request.args.get('min_amount', type=float)
    max_amount = request.args.get('max_amount', type=float)
    date_range = request.args.get('date_range')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # Filtres par catégorie
    if category_id and category_id.strip():
        try:
            cat_id = int(category_id)
            query = query.filter_by(category_id=cat_id)
            form.category_id.data = category_id
        except (ValueError, TypeError):
            # Si la conversion échoue, ignorer le filtre
            pass
    
    # Filtre par mode de paiement
    if payment_method:
        query = query.filter_by(payment_method=payment_method)
        form.payment_method.data = payment_method
    
    # Filtres de montant
    if min_amount:
        query = query.filter(Expense.amount >= min_amount)
        form.min_amount.data = min_amount
    if max_amount:
        query = query.filter(Expense.amount <= max_amount)
        form.max_amount.data = max_amount
    
    # Filtres de date
    today = datetime.now().date()
    if date_range:
        form.date_range.data = date_range
        
        if date_range == 'today':
            query = query.filter(Expense.date == today)
        elif date_range == 'this_week':
            start_of_week = today - timedelta(days=today.weekday())
            query = query.filter(Expense.date >= start_of_week)
        elif date_range == 'this_month':
            start_of_month = today.replace(day=1)
            query = query.filter(Expense.date >= start_of_month)
        elif date_range == 'this_year':
            start_of_year = today.replace(month=1, day=1)
            query = query.filter(Expense.date >= start_of_year)
        elif date_range == 'custom' and start_date and end_date:
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d').date()
                end = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(Expense.date >= start, Expense.date <= end)
                form.start_date.data = start
                form.end_date.data = end
            except ValueError:
                flash('Format de date invalide', 'error')
    
    # Appliquer le tri
    if sort_by == 'amount':
        if sort_order == 'asc':
            query = query.order_by(Expense.amount.asc())
        else:
            query = query.order_by(Expense.amount.desc())
    elif sort_by == 'date':
        if sort_order == 'asc':
            query = query.order_by(Expense.date.asc())
        else:
            query = query.order_by(Expense.date.desc())
    elif sort_by == 'category':
        # Joindre avec la table ExpenseCategory pour trier par nom de catégorie
        query = query.join(ExpenseCategory)
        if sort_order == 'asc':
            query = query.order_by(ExpenseCategory.name.asc())
        else:
            query = query.order_by(ExpenseCategory.name.desc())
    else:
        # Tri par défaut - date descendante
        query = query.order_by(Expense.date.desc())
    
    # Exécuter la requête avec pagination
    paginated_expenses = query.paginate(page=page, per_page=per_page, error_out=False)
    expenses = paginated_expenses.items
    
    # Calculer le montant total de toutes les dépenses (sans pagination)
    total_expenses_query = query.with_entities(func.sum(Expense.amount)).scalar() or 0
    
    return render_template('expenses/index.html', 
                           expenses=expenses,
                           total_amount=total_expenses_query,
                           pagination=paginated_expenses,
                           form=form,
                           sort_by=sort_by,
                           sort_order=sort_order)

@bp.route('/categories', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_expenses')
def categories():
    form = ExpenseCategoryForm()
    
    if form.validate_on_submit():
        category = ExpenseCategory(
            name=form.name.data,
            description=form.description.data,
            owner_id=current_user.id
        )
        
        if form.image.data:
            filename = f"category_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.{form.image.data.filename.split('.')[-1]}"
            form.image.data.save(os.path.join('app', 'static', 'uploads', 'categories', filename))
            category.image_path = f"uploads/categories/{filename}"
        
        try:
            db.session.add(category)
            db.session.commit()
            flash('Catégorie créée avec succès.', 'success')
            return redirect(url_for('expenses.categories'))
        except Exception as e:
            db.session.rollback()
            flash('Une erreur est survenue lors de la création de la catégorie.', 'error')
    
    categories = ExpenseCategory.query.filter_by(owner_id=current_user.id).order_by(ExpenseCategory.name).all()
    return render_template('expenses/categories.html', categories=categories, form=form)

@bp.route('/categories/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_expenses')
def edit_category(id):
    category = ExpenseCategory.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = ExpenseCategoryForm(obj=category)
    
    if form.validate_on_submit():
        try:
            category.name = form.name.data
            category.description = form.description.data
            
            if form.image.data:
                if category.image_path:
                    old_image_path = os.path.join('app', 'static', category.image_path)
                    if os.path.exists(old_image_path):
                        os.remove(old_image_path)
                
                filename = f"category_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.{form.image.data.filename.split('.')[-1]}"
                form.image.data.save(os.path.join('app', 'static', 'uploads', 'categories', filename))
                category.image_path = f"uploads/categories/{filename}"
            
            db.session.commit()
            flash('Catégorie mise à jour avec succès.', 'success')
            return redirect(url_for('expenses.categories'))
        except Exception as e:
            db.session.rollback()
            flash('Une erreur est survenue lors de la mise à jour.', 'error')
    
    return render_template('expenses/categories.html', form=form, category=category)

@bp.route('/categories/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_expenses')
def delete_category(id):
    category = ExpenseCategory.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    
    if category.expenses.count() > 0:
        return jsonify({
            'success': False, 
            'message': 'Impossible de supprimer cette catégorie car elle contient des dépenses.'
        }), 400
    
    try:
        if category.image_path:
            image_path = os.path.join('app', 'static', category.image_path)
            if os.path.exists(image_path):
                os.remove(image_path)
        
        db.session.delete(category)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Catégorie supprimée avec succès.'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Une erreur est survenue lors de la suppression.'}), 500

@bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_expenses')
def create():
    form = ExpenseForm()
    form.category_id.choices = [(c.id, c.name) for c in ExpenseCategory.query.filter_by(owner_id=current_user.id).order_by(ExpenseCategory.name)]
    
    if form.validate_on_submit():
        try:
            expense = Expense(
                category_id=form.category_id.data,
                owner_id=current_user.id,
                user_id=current_user.id,
                date=form.date.data,
                amount=form.amount.data,
                description=form.description.data,
                reference=form.reference.data,
                payment_method=form.payment_method.data,
                is_recurring=form.is_recurring.data,
                recurring_interval=form.recurring_interval.data if form.is_recurring.data else None,
                recurring_day=form.recurring_day.data if form.is_recurring.data else None
            )
            
            if form.image.data:
                filename = f"expense_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.{form.image.data.filename.split('.')[-1]}"
                form.image.data.save(os.path.join('app', 'static', 'uploads', 'expenses', filename))
                expense.image_path = f"uploads/expenses/{filename}"
            
            db.session.add(expense)
            db.session.commit()
            flash('Dépense créée avec succès.', 'success')
            return redirect(url_for('expenses.index'))
        except Exception as e:
            db.session.rollback()
            flash('Une erreur est survenue lors de la création de la dépense.', 'error')
    
    return render_template('expenses/form.html', form=form, title="Nouvelle dépense")

@bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_expenses')
def edit(id):
    expense = Expense.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    form = ExpenseForm(obj=expense)
    form.category_id.choices = [(c.id, c.name) for c in ExpenseCategory.query.filter_by(owner_id=current_user.id).order_by(ExpenseCategory.name)]
    
    if form.validate_on_submit():
        try:
            form.populate_obj(expense)
            
            if form.image.data:
                if expense.image_path:
                    old_image_path = os.path.join('app', 'static', expense.image_path)
                    if os.path.exists(old_image_path):
                        os.remove(old_image_path)
                
                filename = f"expense_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.{form.image.data.filename.split('.')[-1]}"
                form.image.data.save(os.path.join('app', 'static', 'uploads', 'expenses', filename))
                expense.image_path = f"uploads/expenses/{filename}"
            
            if not form.is_recurring.data:
                expense.recurring_interval = None
                expense.recurring_day = None
            
            db.session.commit()
            flash('Dépense mise à jour avec succès.', 'success')
            return redirect(url_for('expenses.index'))
        except Exception as e:
            db.session.rollback()
            flash('Une erreur est survenue lors de la mise à jour de la dépense.', 'error')
    
    return render_template('expenses/form.html', form=form, expense=expense, title="Modifier la dépense")

@bp.route('/delete/<int:id>', methods=['POST'])
@login_required
@permission_required('can_manage_expenses')
def delete(id):
    expense = Expense.query.filter_by(id=id, owner_id=current_user.id).first_or_404()
    try:
        if expense.image_path:
            image_path = os.path.join('app', 'static', expense.image_path)
            if os.path.exists(image_path):
                os.remove(image_path)
        
        db.session.delete(expense)
        db.session.commit()
        flash('Dépense supprimée avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Une erreur est survenue lors de la suppression.', 'error')
    return redirect(url_for('expenses.index')) 

@bp.route('/export', methods=['POST'])
@login_required
@permission_required('can_manage_expenses')
def export():
    export_format = request.form.get('format', 'csv')
    include_images = request.form.get('include_images') == 'on'
    
    # Get expenses data
    expenses = Expense.query.filter_by(owner_id=current_user.id).order_by(Expense.date.desc()).all()
    
    # Prepare headers and data
    headers = ['Date', 'Catégorie', 'Description', 'Référence', 'Montant', 'Mode de paiement', 'Récurrent']
    data = []
    
    for expense in expenses:
        recurring_info = '-'
        if expense.is_recurring:
            if expense.recurring_interval == 'monthly':
                recurring_info = f'Mensuel (J{expense.recurring_day})'
            elif expense.recurring_interval == 'quarterly':
                recurring_info = f'Trimestriel (J{expense.recurring_day})'
            else:
                recurring_info = f'Annuel (J{expense.recurring_day})'
        
        payment_method_map = {
            'cash': 'Espèces',
            'card': 'Carte',
            'transfer': 'Virement',
            'check': 'Chèque',
            'other': 'Autre'
        }
        
        row = [
            expense.date.strftime('%d/%m/%Y'),
            expense.category.name,
            expense.description,
            expense.reference or '-',
            f"{expense.amount:.2f} €",
            payment_method_map.get(expense.payment_method, 'Autre'),
            recurring_info
        ]
        data.append(row)
    
    if export_format == 'csv':
        output = io.StringIO()
        writer = csv.writer(output, delimiter=';', quoting=csv.QUOTE_MINIMAL)
        writer.writerow(headers)
        writer.writerows(data)
        
        output.seek(0)
        return send_file(
            io.BytesIO(output.getvalue().encode('utf-8-sig')),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f'expenses_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        )
    
    elif export_format == 'excel':
        wb = Workbook()
        ws = wb.active
        ws.title = "Dépenses"
        
        # Write headers
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # Write data
        for row_idx, row_data in enumerate(data, 2):
            for col_idx, cell_value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=cell_value)
        
        # Auto-adjust column widths
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
        
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'expenses_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )
    
    elif export_format == 'pdf':
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        elements = []
        
        # Convert data to table format
        table_data = [headers] + data
        table = Table(table_data)
        
        # Add style to table
        style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 12),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ])
        table.setStyle(style)
        elements.append(table)
        
        # Build PDF
        doc.build(elements)
        buffer.seek(0)
        
        return send_file(
            buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'expenses_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )
    
    flash('Format d\'export non supporté.', 'error')
    return redirect(url_for('expenses.index')) 