{% extends "online_ordering_sites/base.html" %}

{% block title %}{{ site.site_name or site.owner.username }} - Commande en ligne{% endblock %}

{% block content %}
<!-- Message de succès de commande -->
{% if request.args.get('order_success') %}
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="alert-heading mb-2">
                    <i class="fas fa-check-circle me-2"></i>
                    Commande confirmée !
                </h5>
                <p class="mb-0">
                    Votre commande #{{ request.args.get('order_success') }} a été reçue avec succès.
                    Vous recevrez des notifications sur l'avancement de votre commande.
                </p>
            </div>
            <div class="col-md-4 text-md-end mt-2 mt-md-0">
                <a href="{{ url_for('online_ordering_sites.track_order', order_number=request.args.get('order_success')) }}"
                   class="btn btn-success">
                    <i class="fas fa-search-location me-2"></i>
                    Suivre ma commande
                </a>
            </div>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{% endif %}

<!-- Hero Section -->
<section class="hero-section py-5" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
    <div class="container">
        <div class="row align-items-center text-white">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-3">
                    Bienvenue chez {{ site.site_name or site.owner.username }}
                </h1>
                {% if site.site_description %}
                    <p class="lead mb-4">{{ site.site_description }}</p>
                {% endif %}
                <div class="d-flex gap-3 flex-wrap">
                    <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-list me-2"></i>Voir le Menu
                    </a>
                    {% if site.allow_delivery %}
                        <span class="badge bg-success fs-6 py-2 px-3">
                            <i class="fas fa-truck me-1"></i>Livraison disponible
                        </span>
                    {% endif %}
                    {% if site.allow_pickup %}
                        <span class="badge bg-info fs-6 py-2 px-3">
                            <i class="fas fa-shopping-bag me-1"></i>À emporter
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-6 text-center">
                {% if site.banner_path %}
                    <img src="{{ url_for('static', filename='uploads/' + site.banner_path) }}"
                         alt="Banner" class="img-fluid rounded shadow">
                {% else %}
                    <i class="fas fa-utensils fa-10x opacity-50"></i>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center">
            {% if site.allow_delivery %}
            <div class="col-md-3 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-truck fa-3x text-primary mb-3"></i>
                        <h5>Livraison</h5>
                        <p class="text-muted">
                            {% if site.delivery_fee > 0 %}
                                Frais de livraison: {{ "%.2f"|format(site.delivery_fee) }}€
                            {% else %}
                                Livraison gratuite
                            {% endif %}
                        </p>
                        {% if site.minimum_order > 0 %}
                            <small class="text-muted">Commande minimum: {{ "%.2f"|format(site.minimum_order) }}€</small>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
            
            {% if site.allow_pickup %}
            <div class="col-md-3 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-shopping-bag fa-3x text-primary mb-3"></i>
                        <h5>À emporter</h5>
                        <p class="text-muted">Récupérez votre commande sur place</p>
                        <small class="text-muted">Prêt en 15-30 minutes</small>
                    </div>
                </div>
            </div>
            {% endif %}
            
            {% if site.allow_dine_in %}
            <div class="col-md-3 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-chair fa-3x text-primary mb-3"></i>
                        <h5>Sur place</h5>
                        <p class="text-muted">Commandez depuis votre table</p>
                        <small class="text-muted">Service à table</small>
                    </div>
                </div>
            </div>
            {% endif %}
            
            {% if site.allow_drive_through %}
            <div class="col-md-3 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-car fa-3x text-primary mb-3"></i>
                        <h5>Drive</h5>
                        <p class="text-muted">Récupération au volant</p>
                        <small class="text-muted">Service rapide</small>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Categories Section -->
{% if categories %}
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Nos Catégories</h2>
        <div class="row">
            {% for category in categories %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <a href="{{ url_for('online_ordering_sites.category_products', category_id=category.id) }}" 
                   class="text-decoration-none">
                    <div class="card category-card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            {% if category.image_path %}
                                <img src="{{ url_for('static', filename='uploads/' + category.image_path) }}" 
                                     alt="{{ category.name }}" class="img-fluid rounded mb-3" style="height: 80px; object-fit: cover;">
                            {% else %}
                                <i class="fas fa-utensils fa-3x text-primary mb-3"></i>
                            {% endif %}
                            <h5 class="card-title">{{ category.name }}</h5>
                            {% if category.description %}
                                <p class="card-text text-muted small">{{ category.description[:50] }}...</p>
                            {% endif %}
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Featured Products Section -->
{% if featured_products %}
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">Nos Spécialités</h2>
        <div class="row">
            {% for product in featured_products %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card h-100 border-0 shadow-sm">
                    {% if product.image_path %}
                        <img src="{{ url_for('static', filename='uploads/' + product.image_path) }}" 
                             class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    {% endif %}
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ product.name }}</h5>
                        {% if product.description %}
                            <p class="card-text text-muted small flex-grow-1">{{ product.description[:80] }}...</p>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <span class="h5 text-primary mb-0">{{ "%.2f"|format(product.price) }}€</span>
                            {% if product.is_available %}
                                <button class="btn btn-primary btn-sm" 
                                        onclick="addToCart({{ product.id }}, '{{ product.name }}', {{ product.price }})">
                                    <i class="fas fa-plus me-1"></i>Ajouter
                                </button>
                            {% else %}
                                <span class="badge bg-secondary">Indisponible</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-list me-2"></i>Voir tout le menu
            </a>
        </div>
    </div>
</section>
{% endif %}

<!-- Call to Action -->
<section class="py-5 bg-primary text-white">
    <div class="container text-center">
        <h2 class="mb-3">Prêt à commander ?</h2>
        <p class="lead mb-4">Découvrez notre menu complet et passez votre commande en quelques clics</p>
        <div class="d-flex gap-3 justify-content-center flex-wrap">
            <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-light btn-lg">
                <i class="fas fa-list me-2"></i>Parcourir le Menu
            </a>
            {% if not session.customer_id %}
                <a href="{{ url_for('online_ordering_sites.customer_register') }}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-user-plus me-2"></i>Créer un Compte
                </a>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        min-height: 60vh;
        display: flex;
        align-items: center;
    }
    
    .category-card:hover,
    .product-card:hover {
        transform: translateY(-5px);
        transition: transform 0.3s ease;
    }
    
    .fa-10x {
        font-size: 10em;
    }
</style>
{% endblock %}
